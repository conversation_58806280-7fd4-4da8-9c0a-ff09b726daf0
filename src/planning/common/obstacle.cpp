// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/common/obstacle.hpp"

#include <algorithm>
#include <utility>

#include "src/common/config/vehicle_config_helper.hpp"   // VehicleConfigHelper
#include "src/common/math/vec2d.hpp"                     // Vec2d
#include "src/planning/config/miscellaneous_config.hpp"  // MiscellaneousConfig
#include "src/planning/planning_module_exception.hpp"    // PlanningModuleException

namespace t2::planning {

using ::t2::common::math::Vec2d;

using ::t2::common::config::VehicleConfigHelper;
// using apollo::common::util::FindOrDie;
// using apollo::perception::PerceptionObstacle;
// using apollo::prediction::ObstaclePriority;

template <typename T>
bool IsValidTrajectory(const T& trajectory) {
  for (const auto& point : trajectory.trajectory_point) {
    if (!(std::isfinite(point.path_point.x) && std::isfinite(point.path_point.y) &&
          std::isfinite(point.path_point.z) && std::isfinite(point.path_point.kappa) &&
          std::isfinite(point.path_point.s) && std::isfinite(point.path_point.dkappa) &&
          std::isfinite(point.path_point.ddkappa) && std::isfinite(point.v) &&
          std::isfinite(point.a) && std::isfinite(point.relative_time))) {
      return false;
    }
  }
  return true;
}

// Either return a map of obstacles or an error value
ObstacleResult CreateObstacles(const PredictionObstacles& prediction_obstacles) {
  ObstacleResult obstacle_result;
  auto& [map_obstacles, error_flags] = obstacle_result;

  for (const auto& prediction_obstacle : prediction_obstacles.prediction_obstacles) {
    const auto& perception_obstacle = prediction_obstacle.perception_obstacle;
    const int id = perception_obstacle.id;

    Obstacle obs;
    // perception data
    obs.id = id;  // perception_obstacle.id;
    obs.length = perception_obstacle.length;
    obs.width = perception_obstacle.width;
    obs.height = perception_obstacle.height;
    obs.theta = perception_obstacle.theta;
    obs.confidence = perception_obstacle.confidence;
    // obs.confidence_type = perception_obstacle.confidence_type();

    // prediction data
    obs.timestamp = prediction_obstacle.timestamp;

    const auto& prediction_trajectories = prediction_obstacle.trajectory;
    // If the trajecotry from the obstacle is empty

    if (prediction_trajectories.empty()) {
      error_flags.push_back(StatusFlagType::PLANNING_RECEIVED_OBSTACLE_WITHOUT_TRAJECTORY);
      T2_WARN << "Detected empty trajectory for obstacle ID:" << id;
    } else if (prediction_trajectories[0].trajectory_point.size() <
               60) {  // If the trajectory used by the planner has less than 60
                      // points
      error_flags.push_back(StatusFlagType::PLANNING_RECEIVED_OBSTACLE_WITH_TOO_SHORT_TRAJECTORY);
      T2_WARN << "Detected too short trajectory for obstacle ID:" << id;
    } else if (map_obstacles.count(id)) {  // If the obstacle id is already
                                           // contained in the obstacle_result
      error_flags.push_back(StatusFlagType::PLANNING_RECEIVED_OBSTACLES_WITH_SAME_ID);
      T2_WARN << "Detected duplicated id for obstacle ID:" << id;
    } else {
      int trajectory_index = 0;
      auto& trajectories = obs.trajectories;
      for (const auto& prediction_trajectory : prediction_trajectories) {
        if (IsValidTrajectory(prediction_trajectory)) {
          obs.trajectories_probability.emplace_back(prediction_trajectory.probability);
          obs.is_merging.emplace_back(prediction_trajectory.type ==
                                      PredictionTrajectoryType::EARLY_MERGING);
          trajectories.emplace_back();
          auto& trajectory = trajectories.back();
          for (const auto& traj_point : prediction_trajectory.trajectory_point) {
            trajectory.emplace_back();
            auto& point = trajectory.back();
            const auto& path_point = traj_point.path_point;
            point.x = path_point.x;
            point.y = path_point.y;
            point.t = traj_point.relative_time;
            point.v = traj_point.v;
          }
        } else {
          T2_ERROR << "obj: trajectory_index=" << trajectory_index << " TrajectoryPoint: "
                   << rosidl_generator_traits::to_yaml(prediction_trajectory) << " is NOT valid.";
        }  // end-if
        ++trajectory_index;
      }  // end-for
      map_obstacles.emplace(obs.id, obs);
    }  // end-if (prediction_trajectories.empty())
  }  // for (const auto& prediction_obstacle : prediction.prediction_obstacle)
  return obstacle_result;
}

std::optional<ObstacleState> GetClosestObstacleInEgoLane(
    const int i, const double s_ego,
    const std::map<int, ObstacleInformation>& obstacles_information, const bool use_is_merging) {
  std::optional<ObstacleState> closest_obs_info;

  // We verify first the distance with the closest front vehicle presents in the
  // ego lane.
  std::optional<ObstacleInformation> closest_front_obs;
  for (auto const& [key, obstacle_information] : obstacles_information) {
    if (closest_front_obs && obstacle_information.is_in_ego_lane && obstacle_information.d_front) {
      if (obstacle_information.d_front.value() < closest_front_obs->d_front.value()) {
        closest_front_obs = obstacle_information;
      }
    } else if (obstacle_information.is_in_ego_lane && obstacle_information.d_front) {
      closest_front_obs = obstacle_information;
    }
  }
  if (closest_front_obs) {
    closest_obs_info = ObstacleState();
    closest_obs_info->s_front = closest_front_obs->obstacle_trajectory[i].s_front;
    closest_obs_info->d_front = closest_front_obs->obstacle_trajectory[i].s_front - s_ego;
    closest_obs_info->v = closest_front_obs->obstacle_trajectory[i].v;
    closest_obs_info->t = closest_front_obs->obstacle_trajectory[i].t;
  }

  // We then verify the distance with the obstacle that is doing a merging in
  // the ego lane.
  if (use_is_merging) {
    for (auto const& [key, obstacle_information] : obstacles_information) {
      // We consider the obstacle only if it is considered merging. (This is
      // determined by prediction module.)
      if (obstacle_information.is_merging) {
        auto& obstacle_state = obstacle_information.obstacle_trajectory[i];
        const double d_front = obstacle_state.s_front - s_ego;
        // We only consider the obstalce if d_front is positive and
        // its position is predicted to be in the ego lane.
        if (obstacle_state.is_in_ego_lane && d_front > 0.0) {
          if (closest_obs_info) {
            if (d_front < closest_obs_info->d_front) {
              closest_obs_info->s_front = obstacle_state.s_front;
              closest_obs_info->d_front = d_front;
              closest_obs_info->v = obstacle_state.v;
              closest_obs_info->t = obstacle_state.t;
            }
          } else {
            closest_obs_info = ObstacleState();
            closest_obs_info->s_front = obstacle_state.s_front;
            closest_obs_info->d_front = d_front;
            closest_obs_info->v = obstacle_state.v;
            closest_obs_info->t = obstacle_state.t;
          }
        }
      }
    }
  }
  return closest_obs_info;
}

std::vector<std::vector<TrajectorySLPoint>> ComputeSL(const ReferenceLine& reference_line,
                                                      const Obstacle& obstacle) {
  const auto& trajectories = obstacle.trajectories;
  const size_t n_trajectories = trajectories.size();

  std::vector<std::vector<TrajectorySLPoint>> vv_sl_points(n_trajectories);
  for (size_t i = 0; i < n_trajectories; ++i) {
    const size_t n_points = trajectories[i].size();
    std::vector<TrajectorySLPoint> vec_sl_points(n_points);
    for (size_t j = 0; j < n_points; ++j) {
      auto& sl_point = vec_sl_points[j];
      const auto& xy_point = trajectories[i][j];
      common::math::Vec2d v(xy_point.x, xy_point.y);
      const std::optional<SLPoint> opt_sl_point = reference_line.XYToSL(v);
      sl_point.s = opt_sl_point->s;
      sl_point.l = opt_sl_point->l;
      sl_point.t = xy_point.t;
    }
    vv_sl_points[i] = vec_sl_points;
  }
  return vv_sl_points;
}

std::map<int, std::vector<std::vector<TrajectorySLPoint>>> ComputeSlTrajectoriesObstacles(
    const std::map<int, Obstacle>& obstacle_map, const ReferenceLine& reference_line) {
  std::map<int, std::vector<std::vector<TrajectorySLPoint>>> sl_trajectories;
  for (auto const& [key, obs] : obstacle_map) {
    sl_trajectories.emplace(obs.id, ComputeSL(reference_line, obs));
  }
  return sl_trajectories;
}

std::map<int, ObstacleInformation> ComputeObstaclesInformation(
    const std::map<int, Obstacle>& obstacle_map,
    const ReferenceLineAndRouteSegments& ego_reference_line,
    const std::optional<ReferenceLineAndRouteSegments>& ego_right_reference_line,
    const std::optional<ReferenceLineAndRouteSegments>& ego_left_reference_line,
    const double& ego_s, const double& ego_l, const double& ego_lane_right_width,
    const double& ego_lane_left_width, const PlannerConfig& planner_config) {
  // apollo/modules/common/data/vehicle_param.pb.txt
  const auto& vehicle_config = VehicleConfigHelper::GetInstance().GetConfig();
  const double front_edge_to_center = vehicle_config.vehicle_param().front_edge_to_center();
  const double back_edge_to_center = vehicle_config.vehicle_param().back_edge_to_center();

  // output
  std::map<int, ObstacleInformation> obstacles_information;

  // processing

  const std::map<int, std::vector<std::vector<TrajectorySLPoint>>> sl_trajectories =
      ComputeSlTrajectoriesObstacles(obstacle_map, ego_reference_line.reference_line);

  for (auto const& [key, obs] : obstacle_map) {
    if (sl_trajectories.count(key) == 1 && sl_trajectories.at(key).size()) {
      // we get the lateral_margin which correspond to the type of obstacle
      const double lateral_margin = GetLateralMargin(obs, planner_config);

      // we select the trajectory with the highest probability.
      const int highest_probability_traj_idx =
          std::max_element(obs.trajectories_probability.begin(),
                           obs.trajectories_probability.end()) -
          obs.trajectories_probability.begin();
      // we take the trajectory with the highest probability.
      const auto& traj = sl_trajectories.at(key)[highest_probability_traj_idx];
      const auto& traj_xy = obs.trajectories[highest_probability_traj_idx];
      // obstacle pos
      // obstacle front edge position
      double obs_front_s = traj[0].s + obs.length / 2.0;
      // obstacle rear edge position
      double obs_rear_s = traj[0].s - obs.length / 2.0;
      // obstacle center position
      double obs_middle_s = traj[0].s;
      // ego pos
      // ego front edge position
      double ego_front_s = ego_s + front_edge_to_center;
      // ego rear edge position
      double ego_rear_s = ego_s - back_edge_to_center;
      // front obstacle
      double dist_ego_front = obs_rear_s - ego_front_s;
      // rear obstacle
      double dist_ego_rear = obs_front_s - ego_rear_s;

      // Create the struct which contains the obstacle informations.
      ObstacleInformation obstacle_information;
      obstacle_information.s = traj[0].s;
      obstacle_information.is_merging = obs.is_merging[highest_probability_traj_idx];

      // Trajectory
      for (std::vector<TrajectoryPoint>::size_type i = 0; i < traj.size(); ++i) {
        const auto& point = traj[i];
        auto obstacle_state = ObstacleState();
        obstacle_state.s = point.s - ego_s;

        // obstacle rear edge position
        const double _obs_rear_s = point.s - obs.length / 2.0;
        const double _ego_front_s = ego_s + front_edge_to_center;
        obstacle_state.s_front = _obs_rear_s - _ego_front_s;
        obstacle_state.l = point.l;
        obstacle_state.t = point.t;
        obstacle_state.v = traj_xy[i].v;
        obstacle_state.l_ego_reference_line = obstacle_state.l;
        obstacle_state.right_edge_position = obstacle_state.l_ego_reference_line - obs.width / 2.0;
        obstacle_state.left_edge_position = obstacle_state.l_ego_reference_line + obs.width / 2.0;

        // If the obstacle overlaps with the ego lane. Currently, there is no
        // tolerance buffer so even if the obstacle overlap is extremely
        // small, the obstacle will be considered in the ego lane.
        // ego_lane_right_width and ego_lane_left_width are positive values.
        if (obstacle_state.left_edge_position > -ego_lane_right_width + lateral_margin &&
            obstacle_state.right_edge_position < ego_lane_left_width - lateral_margin) {
          obstacle_state.is_in_ego_lane = true;
        }
        obstacle_information.obstacle_trajectory.emplace_back(obstacle_state);
      }

      // lateral distances
      obstacle_information.l_ego_reference_line = traj[0].l;
      obstacle_information.l_in_ego_frame = traj[0].l - ego_l;

      // longitudinal distances of the obstacle with the ego vehicle.
      obstacle_information.d_center = obs_middle_s - ego_s;
      if (dist_ego_front > 0.0) {
        obstacle_information.d_front = dist_ego_front;
      } else if (dist_ego_rear < 0.0) {
        obstacle_information.d_rear = fabs(dist_ego_rear);
      }

      // velocity
      obstacle_information.linear_velocity = obs.trajectories[0][0].v;

      // dimensions
      obstacle_information.length = obs.length;
      obstacle_information.width = obs.width;
      obstacle_information.right_edge_position =
          obstacle_information.l_ego_reference_line - obstacle_information.width / 2.0;
      obstacle_information.left_edge_position =
          obstacle_information.l_ego_reference_line + obstacle_information.width / 2.0;

      // VERIFY IN WHICH LANE THE OBSTACLE IS
      if (ego_right_reference_line) {
        // Get the lane at the right of the ego lane width.
        double right_obstacle_lane_right_width = 0.0;
        double right_obstacle_lane_left_width = 0.0;
        // ego_right_reference_line->reference_line.GetLaneWidth(obstacle_information.s,
        //                                                       right_obstacle_lane_left_width,
        //                                                       right_obstacle_lane_right_width);
        double right_lane_limit = -ego_lane_right_width - right_obstacle_lane_right_width -
                                  right_obstacle_lane_left_width;

        // Verify if the obstacle is in the right ego lane.
        if (obstacle_information.right_edge_position < -ego_lane_right_width - lateral_margin &&
            obstacle_information.left_edge_position > right_lane_limit + lateral_margin) {
          obstacle_information.is_in_right_ego_lane = true;
        }
      }

      if (ego_left_reference_line) {
        // Get the lane at the left of the ego lane width.
        double left_obstacle_lane_right_width = 0.0;
        double left_obstacle_lane_left_width = 0.0;
        // ego_left_reference_line->reference_line.GetLaneWidth(
        //     obstacle_information.s, left_obstacle_lane_left_width,
        //     left_obstacle_lane_right_width);
        double left_lane_limit =
            ego_lane_left_width + left_obstacle_lane_right_width + left_obstacle_lane_left_width;

        // Verify if the obstacle is in the left ego lane.
        if (obstacle_information.left_edge_position > ego_lane_left_width + lateral_margin &&
            obstacle_information.right_edge_position < left_lane_limit - lateral_margin) {
          obstacle_information.is_in_left_ego_lane = true;
        }
      }

      // Verify if the obstacle is in the ego lane.
      obstacle_information.is_in_ego_lane = false;
      if (obstacle_information.left_edge_position > -ego_lane_right_width + lateral_margin &&
          obstacle_information.right_edge_position < ego_lane_left_width - lateral_margin) {
        obstacle_information.is_in_ego_lane = true;
      }

      // add the ObstacleInformation object.
      obstacles_information.emplace(obs.id, obstacle_information);
    }
  }

  return obstacles_information;
}

double GetLateralMargin(const Obstacle&, const PlannerConfig& planner_config) {
  return planner_config.lateral_margin;
}

}  // namespace t2::planning
