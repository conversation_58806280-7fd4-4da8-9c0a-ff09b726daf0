// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "truckmaker_component.hpp"  // TruckMakerComponent

#include <fstream>  // std::ifstream

#include <arpa/inet.h>

#include "src/planning/common/json_string.hpp"         // ToJsonString, FromJsonString
#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException

/*

Usage:

t2_sils_launch
/apollo/src/planning/truckmaker/spawn/truckmaker_scenario.sils.txtpb

*/

#define THROW_EXCEPTION_WITH_ERROR_MESSAGE(MSG) \
  T2_ERROR << (MSG);                            \
  throw planning::PlanningModuleException(MSG, PlanningModuleErrorCode::UNEXPECTED_STATE);

namespace t2::planning::truckmaker {

TruckMakerComponent::~TruckMakerComponent() {
  // Cleanup: close sockets if still open
  if (client_socket_ >= 0) {
    T2_INFO << "close client_socket_=" << client_socket_;
    close(client_socket_);
  }
  if (server_fd_ >= 0) {
    T2_INFO << "close server_fd_=" << server_fd_;
    close(server_fd_);
  }
}

TruckMakerComponent::TruckMakerComponent()
    : base{get_rclcpp_node()}, node_(get_rclcpp_node()), address_len_{sizeof(address_)} {
  /* ===== Writers =====*/
  // tf2_broadcaster = init_context.CreateTransformBroadcaster(false);
  truckmaker_data_in_writer =
      node_.create_publisher<TruckMakerMessageIn>(kTruckMakerDataInTopic, rclcpp::DefaultQoS());

  truckmaker_data_out_writer =
      node_.create_publisher<TruckMakerMessageOut>(kTruckMakerDataOutTopic, rclcpp::DefaultQoS());

  localization_writer =
      node_.create_publisher<LocalizationEstimate>(kLocalizationPoseTopic, rclcpp::DefaultQoS());

  /* ===== Readers =====*/
  // control_command_reader = init_context.CreateReader<control::ControlCommand>(
  //     kControlTopic,
  //     [this](const std::shared_ptr<const control::ControlCommand>& ptr) {
  //       control_command = *ptr;
  //     });

  localization_reader = node_.create_polling_subscription<LocalizationEstimate>(
      kLocalizationPoseTopic, rclcpp::DefaultQoS()
      // init_context.CreateReader<LocalizationEstimate>(
      //     kLocalizationPoseTopic,
      //     [this](
      //         const std::shared_ptr<const LocalizationEstimate>&
      //             ptr) {
      //       if (!has_init_localization_info_) {
      //         localization_info = LocalizationInfo(*ptr);
      //         has_init_localization_info_ = true;
      //       }
      //     }
  );

  /* ===== TruckMaker Config (Read-only) =====*/
  {
    std::ifstream is(kTruckMakerConfigFile);
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
        << "Cannot read " << kTruckMakerConfigFile;
    cereal::JSONInputArchive archive(is);  // serialize to JSON
    archive(CEREAL_NVP(truckmaker_config));
    T2_INFO << "TruckMakerConfig is:\n" << ToJsonString(truckmaker_config);
  }

  // Create the server socket
  CreateServerSocket_();

  // Bind and listen
  BindAndListen_(truckmaker_config.port_number);

  // Accept one client
  AcceptClient_();

  /* ===== Set up callback ====*/
  // using std::chrono_literals::operator""ms;
  //   init_context.RegisterTimerEventCallback(
  //       "ProcTruckMaker", [&]() { Proc(); },
  //       10ms);  ///< same as TruckMaker bridge
}

void TruckMakerComponent::Proc() {
  const int val_read = recv(client_socket_, &truckmaker_data_out_, sizeof(truckmaker_data_out_), 0);

  UpdateLocalizationFromTruckMakerDataOut(truckmaker_data_out_);
  localization_writer->publish(localization_info.ToProto());

  if (val_read <= 0) {
    T2_ERROR << "Client disconnected or error occurred. Stopping...";
    return;
  }

  truckmaker_data_out_writer->publish(ToMessage(truckmaker_data_out_));
  truckmaker_data_in_writer->publish(ToMessage(truckmaker_data_in_));

  /*
    TBI: Set target yaw rate, steer angle, and acceleration to TruckMaker
  */
  memset(&truckmaker_data_in_, 0, sizeof(truckmaker_data_in_));
  // truckmaker_data_in_.tgt_yawrate = control_command.angular_velocity();
  // truckmaker_data_in_.tgt_steer_angle = control_command.steering_angle_rad();
  // truckmaker_data_in_.tgt_accl = control_command.acceleration();
  // T2_INFO << "tgt_steer_angle = " << control_command.steering_angle_rad();
  send(client_socket_, &truckmaker_data_in_, sizeof(truckmaker_data_in_), 0);
}

void TruckMakerComponent::CreateServerSocket_() {
  // Create socket
  server_fd_ = socket(AF_INET, SOCK_STREAM, 0);
  if (server_fd_ < 0) {
    THROW_EXCEPTION_WITH_ERROR_MESSAGE("Failed to create socket.");
  }

  T2_INFO << "Successfully created socket: server_fd_=" << server_fd_;

  // Optionally set SO_REUSEADDR | SO_REUSEPORT
  int opt = 1;
  if (setsockopt(server_fd_, SOL_SOCKET, SO_REUSEADDR | SO_REUSEPORT, &opt, sizeof(opt)) < 0) {
    THROW_EXCEPTION_WITH_ERROR_MESSAGE("Failed to set socket options.");
  }

  T2_INFO << "Successfully set socket options!";
}

void TruckMakerComponent::BindAndListen_(const int port) {
  // Initialize server address structure
  std::memset(&address_, 0, sizeof(address_));
  address_.sin_family = AF_INET;
  address_.sin_addr.s_addr = INADDR_ANY;
  address_.sin_port = htons(port);

  // Bind socket
  if (bind(server_fd_, reinterpret_cast<struct sockaddr*>(&address_), sizeof(address_)) < 0) {
    THROW_EXCEPTION_WITH_ERROR_MESSAGE("Failed to bind socket.");
  }

  T2_INFO << "Successfully bound socket to address";

  // Listen
  if (listen(server_fd_, 3) < 0) {
    THROW_EXCEPTION_WITH_ERROR_MESSAGE("Failed to listen on socket.");
  }

  T2_INFO << "Server listening on port " << port << "...";
}

void TruckMakerComponent::AcceptClient_() {
  const auto socket_timeout = truckmaker_config.socket_timeout;
  // Set a timeout (SO_RCVTIMEO) on the socket
  struct timeval tv;
  tv.tv_sec = socket_timeout;
  tv.tv_usec = 0;
  setsockopt(server_fd_, SOL_SOCKET, SO_RCVTIMEO, (const char*)&tv, sizeof(tv));

  T2_INFO << "Waiting accept for " << time_t2string(socket_timeout) + " seconds";
  client_socket_ = accept(server_fd_, reinterpret_cast<struct sockaddr*>(&address_), &address_len_);
  if (client_socket_ < 0) {
    const std::string msg =
        "Failed to accept client connection after " + time_t2string(socket_timeout) + " seconds";
    THROW_EXCEPTION_WITH_ERROR_MESSAGE(msg);
  }
  T2_INFO << "Client connection accepted: client_socket_=" << client_socket_;

  // Optionally do an initial send
  memset(&truckmaker_data_in_, 0, sizeof(truckmaker_data_in_));
  send(client_socket_, &truckmaker_data_in_, sizeof(truckmaker_data_in_), 0);
}

void TruckMakerComponent::UpdateLocalizationFromTruckMakerDataOut(
    const TruckMakerDataOut& truckmaker_data_out) {
  // update localization
  auto& theta = localization_info.heading;
  const auto& next_vhcl = truckmaker_data_out.next_vhcl;
  const double dx = next_vhcl.dx,
               dy = next_vhcl.dy;  ///< these are in the Truck's old frame
  double cth = cos(theta), sth = sin(theta);

  // update positions
  localization_info.x += cth * dx - sth * dy;
  localization_info.y += sth * dx + cth * dy;

  // yaw and velocity are in the Truck's new frame
  theta += next_vhcl.dyaw;
  cth = cos(theta);
  sth = sin(theta);

  // [vx, vy] is from Truck's frame, and
  // we put back to the Apollo's frame
  // this is the correct infomation used by Control
  localization_info.vx = cth * next_vhcl.vx - sth * next_vhcl.vy;
  localization_info.vy = sth * next_vhcl.vx + cth * next_vhcl.vy;
  localization_info.vz = 0.0;

  constexpr double quarter_pi = M_PI * 0.25;
  constexpr double half_pi = M_PI * 0.5;
  // quat = [w,x,y,z] = [cos((θ-pi/2)/2), 0, 0, sin((θ-pi/2)/2)]
  localization_info.qw = cos(theta * 0.5 - quarter_pi);
  localization_info.qz = sin(theta * 0.5 - quarter_pi);
  localization_info.qx = localization_info.qy = 0.0;

  // vehicle reference frame; see planning_vehicle_state_creator.cc
  localization_info.ay =
      sqrt(next_vhcl.ax * next_vhcl.ax + next_vhcl.ay * next_vhcl.ay + next_vhcl.az * next_vhcl.az);

  const double v_yaw = std::atan2(localization_info.vy, localization_info.vx);
  const double v_heading = v_yaw - half_pi;
  const double angle = v_heading - theta;  ///< + if v leans toward left
  T2_INFO << std::setprecision(12)
          << "UpdateLocalizationFromTruckMakerDataOut: x=" << localization_info.x
          << ", y=" << localization_info.y << ", heading=" << theta << ", v_heading=" << v_heading
          << ", angle=" << angle;

  // transform_stamped_.source = "novatel";
  // transform_stamped_.target = "world";
  // transform_stamped_.transform.setScale(1);

  // BroadcastTransform_(localization_info, transform_stamped_);
}

// void TruckMakerComponent::BroadcastTransform_(
//     const LocalizationInfo& localization_info_in,
//     t2::adapter::TransformStamped& transform_stamped) const {
//   // Refer to DynamicsSimulatorComponent::Process
//   T2_INFO << "localization_info.x, y, z = " << localization_info_in.x << ", "
//           << localization_info_in.y << ", " << localization_info_in.z;
//   transform_stamped.time = GetClock().Now();
//   transform_stamped.transform.setOrigin(t2::adapter::Vector3(
//       localization_info_in.x, localization_info_in.y, localization_info_in.z));
//   transform_stamped.transform.setRotation(t2::adapter::Quaternion(
//       localization_info_in.qx, localization_info_in.qy, localization_info_in.qz,
//       localization_info_in.qw));
//   tf2_broadcaster->SendTransform(transform_stamped);
// }

}  // namespace t2::planning::truckmaker
