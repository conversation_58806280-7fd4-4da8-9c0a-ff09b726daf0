// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <planning_msgs/msg/truck_maker_message_in.hpp>   // TruckMakerMessageIn
#include <planning_msgs/msg/truck_maker_message_out.hpp>  // TruckMakerMessageOut

#include "common/truckmaker_data.hpp"  // TruckMakerData*

namespace t2::planning::truckmaker {

using TruckMakerMessageIn = ::planning_msgs::msg::TruckMakerMessageIn;
using TruckMakerMessageOut = ::planning_msgs::msg::TruckMakerMessageOut;

TruckMakerMessageIn ToMessage(const TruckMakerDataIn& in);

TruckMakerMessageOut ToMessage(const TruckMakerDataOut& in);

TruckMakerDataIn FromMessage(const TruckMakerMessageIn& in);

TruckMakerDataOut FromMessage(const TruckMakerMessageOut& in);

std::string time_t2string(std::time_t val);

}  // namespace t2::planning::truckmaker
