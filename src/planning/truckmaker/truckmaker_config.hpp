// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <cereal/archives/json.hpp>
#include <cereal/types/memory.hpp>
#include <cereal/types/vector.hpp>

namespace t2::planning::truckmaker {

struct TruckMakerConfig {
  bool use_truckmaker = false;   ///< whether use TruckMakerComponent in
                                 ///< PlanningTestSimulationComponent
  int port_number = 55000;       ///< fixed port for forwarding
  time_t socket_timeout = 1000;  ///< timeout after calling accept

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(use_truckmaker), CEREAL_NVP(port_number), CEREAL_NVP(socket_timeout));
  }
};  // struct TruckMakerConfig

}  // namespace t2::planning::truckmaker
