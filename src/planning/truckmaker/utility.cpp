// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "utility.hpp"  // ToMessage, FromMessage

namespace t2::planning::truckmaker {

TruckMakerMessageIn ToMessage(const TruckMakerDataIn& in) {
  TruckMakerMessageIn out;
#define SET_PROTO_VALUE(X) out.X = in.X
  SET_PROTO_VALUE(tgt_yawrate);
  SET_PROTO_VALUE(tgt_steer_angle);
  SET_PROTO_VALUE(tgt_accl);
#undef SET_PROTO_VALUE
  return out;
}

TruckMakerMessageOut ToMessage(const TruckMakerDataOut& in) {
  TruckMakerMessageOut out;
  out.dt = in.dt;
  auto& next_vhcl = out.next_vhcl;
  next_vhcl.dx = in.next_vhcl.dx;
  next_vhcl.dy = in.next_vhcl.dy;
  next_vhcl.dz = in.next_vhcl.dz;
  next_vhcl.vx = in.next_vhcl.vx;
  next_vhcl.vy = in.next_vhcl.vy;
  next_vhcl.vz = in.next_vhcl.vz;
  next_vhcl.v = in.next_vhcl.v;
  next_vhcl.ax = in.next_vhcl.ax;
  next_vhcl.ay = in.next_vhcl.ay;
  next_vhcl.az = in.next_vhcl.az;
  next_vhcl.dyaw = in.next_vhcl.dyaw;
  // FrontCar
  out.front_car.d = in.front_car.d;
  out.front_car.v = in.front_car.v;
  // GTLine
  out.gt_line.central_curve.id = in.gt_line.central_curve.id;
  out.gt_line.central_curve.np = in.gt_line.central_curve.np;
  out.gt_line.central_curve.x = {in.gt_line.central_curve.x.begin(),
                                 in.gt_line.central_curve.x.end()};
  out.gt_line.central_curve.y = {in.gt_line.central_curve.y.begin(),
                                 in.gt_line.central_curve.y.end()};
  out.gt_line.central_curve.z = {in.gt_line.central_curve.z.begin(),
                                 in.gt_line.central_curve.z.end()};
  return out;
}

TruckMakerDataIn FromMessage(const TruckMakerMessageIn& in) {
  TruckMakerDataIn out;
#define SET_DATA_VALUE(X) out.X = in.X
  SET_DATA_VALUE(tgt_yawrate);
  SET_DATA_VALUE(tgt_steer_angle);
  SET_DATA_VALUE(tgt_accl);
#undef SET_DATA_VALUE
  return out;
}

template <size_t N>
inline void copy_vec_to_array(std::array<double, N>& arr_out, const std::vector<double>& vec_in) {
  const size_t n = std::min(N, vec_in.size());
  arr_out.fill(0);
  for (size_t i = 0; i < n; ++i) {
    arr_out[i] = vec_in[i];
  }
}

TruckMakerDataOut FromMessage(const TruckMakerMessageOut& in) {
  const auto& next_vhcl = in.next_vhcl;
  const auto& gt_line = in.gt_line;
  const auto& front_car = in.front_car;
  auto out = TruckMakerDataOut{.dt = in.dt,
                               .next_vhcl = NextVhclState{.dx = next_vhcl.dx,
                                                          .dy = next_vhcl.dy,
                                                          .dz = next_vhcl.dz,
                                                          .vx = next_vhcl.vx,
                                                          .vy = next_vhcl.vy,
                                                          .vz = next_vhcl.vz,
                                                          .v = next_vhcl.v,
                                                          .ax = next_vhcl.ax,
                                                          .ay = next_vhcl.ay,
                                                          .az = next_vhcl.az,
                                                          .dyaw = next_vhcl.dyaw},
                               .front_car = FrontCar{.d = front_car.d, .v = front_car.v},
                               .gt_line = GTLine{Line{}}};
  out.gt_line.central_curve.id = in.gt_line.central_curve.id;
  out.gt_line.central_curve.np = in.gt_line.central_curve.np;
  copy_vec_to_array(out.gt_line.central_curve.x, in.gt_line.central_curve.x);
  copy_vec_to_array(out.gt_line.central_curve.y, in.gt_line.central_curve.y);
  copy_vec_to_array(out.gt_line.central_curve.z, in.gt_line.central_curve.z);
  return out;
}

std::string time_t2string(std::time_t val) { return std::to_string(static_cast<int64_t>(val)); }

}  // namespace t2::planning::truckmaker
