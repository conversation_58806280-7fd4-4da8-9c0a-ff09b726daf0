#!/bin/bash

# Rename .cc → .cpp
find . -type f -name "*.cc" | while read -r file; do
  new_file="${file%.cc}.cpp"
  echo "Renaming $file → $new_file"
  mv "$file" "$new_file"
done

# Rename .h → .hpp
find . -type f -name "*.h" | while read -r file; do
  new_file="${file%.h}.hpp"
  echo "Renaming $file → $new_file"
  mv "$file" "$new_file"
done

# Rename BUILD → BUILD.bazel
find . -type f -name "BUILD" | while read -r file; do
  new_file="${file}.bazel"
  echo "Renaming $file → $new_file"
  mv "$file" "$new_file"
done

# Replace .h" with .hpp" and .cc" with .cpp" in all files
find . -type f \( -name "*.cpp" -o -name "*.hpp" -o -name "BUILD.bazel" \) | while read -r file; do
  echo "Updating $file"
  sed -i 's/\.h"/\.hpp"/g' "$file"
  sed -i 's/\.cc"/\.cpp"/g' "$file"
done
