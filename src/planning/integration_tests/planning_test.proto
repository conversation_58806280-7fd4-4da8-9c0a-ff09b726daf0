syntax = "proto2";
package apollo.planning.test;

import "modules/common/proto/header.proto";

message PlanningTestMessage {
  optional apollo.common.Header header = 1;
  optional string filename = 2;      // json file for the scenario
  optional string scenario = 3;      // scenario contents
  optional string command = 4;       // shutdown message
  optional double elapsed_time = 5;  // run time for each cycle
}
