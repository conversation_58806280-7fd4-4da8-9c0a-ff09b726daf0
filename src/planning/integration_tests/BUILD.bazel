load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")
load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")

PLANNING_COPTS = [
    "-DMODULE_NAME=\\\"planning\\\"",
]

cc_binary(
    name = "planning_closed_loop_apex",
    srcs = [
        "planning_closed_loop.hpp",
        "planning_closed_loop_apex.cpp",
        "scenario_file.hpp",  # ScenarioFile
    ],
    copts = PLANNING_COPTS,
    data = [
        ":planning_test_input_filename",
        "//src/planning/closed_loop_scenarios",  # "closed_loop_scenarios/*/*.json"
    ],
    linkopts = [
        "-lGL",
        "-pthread",
    ],
    deps = [
        ":planning_closed_loop_lib",
        "//src/common/health:health_reporter",
        "//src/common/runfiles",  # RuntimeDependencyManager
        "//src/planning:planning_component",  # PlanningComponent
        "//src/planning/dummy_component",  # PlanningDummyComponent
        "@apex//common/cpputils",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@com_google_absl//absl/debugging:failure_signal_handler",
    ],
)

filegroup(
    name = "planning_test_input_filename",
    srcs = glob([
        "*.json",
    ]),
)

process_manager(
    name = "test_planning_closed_loop_apex",
    data = [
        ":param/execution_monitor_service.yaml",
        ":planning_closed_loop_apex",
        "//src/system/cli",
        "@apex//grace/monitoring/execution_monitor_service:execution_monitor_service_exe",
    ],
    launch_file = "launch/planning_closed_loop_apex.launch.yaml",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "planning_closed_loop",
    srcs = [
        "planning_closed_loop.cpp",
        "planning_closed_loop.hpp",
    ],
    copts = PLANNING_COPTS,
    linkopts = [
        "-lGL",
        "-pthread",
    ],
    deps = [
        ":planning_closed_loop_lib",
        "//src/planning/planner/path_planner:path_planner_nlp",  # vec_to_string
    ],
)

cc_binary(
    name = "planning_online_mapping",
    srcs = [
        "planning_closed_loop.hpp",
        "planning_online_mapping.cpp",
        "planning_online_mapping.hpp",
        "scenario_file.hpp",  # ScenarioFile
    ],
    copts = PLANNING_COPTS,
    data = [
        ":planning_test_input_filename",
        "//src/planning/new_arch_scenarios",  # "new_arch_scenarios/*/*.json"
    ],
    linkopts = [
        "-lGL",
        "-pthread",
    ],
    deps = [
        ":planning_closed_loop_lib",
        "//src/common/config:vehicle_config_helper",  # VehicleConfigHelper
        "//src/common/runfiles",  # RuntimeDependencyManager
        "//src/planning:planning_dir_prefix",  # FULL_PLANNING_DIR_PREFIX
        "//src/planning/planning_on_local_map_component:internal_data_definition",
        "//src/planning/planning_on_local_map_component:local_map_component",
        "//src/planning/planning_on_local_map_component:simulation",
        "//src/planning/planning_on_local_map_component/prototype_planner",  # Plan
        "@apex//common/cpputils",
        "@apex//common/interrupt",
        "@apex//grace/execution/apex_init",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/monitoring/execution_monitor_service:execution_monitor_service_exe",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@com_google_absl//absl/debugging:failure_signal_handler",
    ],
)

process_manager(
    name = "test_planning_online_mapping",
    data = [
        ":param/execution_monitor_service.yaml",
        ":planning_online_mapping",
        "//src/system/cli",
        "@apex//grace/monitoring/execution_monitor_service:execution_monitor_service_exe",
    ],
    launch_file = "launch/planning_online_mapping.launch.yaml",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "libplanning_test_simulation_component.so",
    linkshared = True,
    linkstatic = False,
    deps = [
        ":planning_closed_loop_lib",
    ],
)

cc_library(
    name = "planning_closed_loop_lib",
    srcs = [
        "planning_hdmap_utils.cpp",
        "planning_test_input.cpp",
        "planning_test_simulation_component.cpp",
    ],
    hdrs = [
        "planning_hdmap_utils.hpp",  # map functions
        "planning_test_input.hpp",  # PlanningTestInput
        "planning_test_simulation_component.hpp",  # PlanningTestSimulationComponent
        "timer.hpp",  # Timer
    ],
    copts = PLANNING_COPTS,
    data = [
        "//src/planning:runtime_data",  # "conf/*.json"
    ],
    deps = [
        "//src/interfaces/planning_msgs",  # planning_msgs::msg::*
        "//src/interfaces/planning_trajectory_msgs",  # planning_trajectory_msgs::msg::*
        "//src/map/hdmap:hdmap_util",
        "//src/planning/config:planning_config",  # PlanningConfig
        "//src/planning/common:input_message_info",  # ChassisInfo, LocalizationInfo, PlanningProcInput
        "//src/planning/common:obstacle",
        "//src/planning/common:trajectory_stitcher",
        "//src/planning/common/planning_vehicle_state",
        "//src/planning/create_intentions",
        "//src/planning/lane_change_decider",
        "//src/planning/lane_change_decider:lane_change",
        "//src/planning/planner",
        "//src/planning/reference_line",
        "//src/planning/reference_line:reference_line_provider",
        "//src/planning/select_trajectory",
        "//src/planning:planning_dir_prefix",  # FULL_PLANNING_DIR_PREFIX
        "//src/common/core",  # T2_*
        "//src/common/config:map_config",  # MapConfig
        "//src/planning/truckmaker:utility",  # FromProto, ToProto
        "//src/planning/truckmaker:truckmaker_config",  # TruckMakerConfig
        "//src/common/runfiles",  # RuntimeDependencyManager
        # ========================
        # Third-party dependencies
        # ========================
        "@ros2.geometry2//tf2_ros:tf2_ros",  # tf2_ros::StaticTransformBroadcaster
        "@apex//common/cpputils",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",  # rclcpp::Node
    ],
)
