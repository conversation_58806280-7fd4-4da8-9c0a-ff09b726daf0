// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <cereal/archives/json.hpp>
#include <cereal/types/string.hpp>  // needed for deserialization

#include "src/common/core/logging.hpp"  // T2_*
namespace t2::planning::test {

template <typename T>
inline bool FromFile(T& o, const std::string& filename) {
  try {
    std::ifstream json_stream(filename);
    cereal::JSONInputArchive archive(json_stream);
    archive(o);
  } catch (const std::exception& e) {
    T2_ERROR << "Error during deserialization: " << e.what();
    return false;
  }
  return true;
}

}  // namespace t2::planning::test
