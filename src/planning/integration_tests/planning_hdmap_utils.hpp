// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <prediction_msgs/msg/prediction_obstacles.hpp>  // PredictionObstacles

#include "src/map/hdmap/hdmap_common.hpp"              // LaneInfo
#include "src/planning/common/input_message_info.hpp"  // LocalizationInfo
#include "src/planning/common/obstacle.hpp"            // TrajectoryXYPoint
#include "src/planning/planner/planner.hpp"            // PlannerConfig

namespace t2::planning::test {

REGISTER_INTER_PREDICTION_MSG(PredictionObstacle);
REGISTER_INTER_PREDICTION_MSG(PredictionObstacles);

void UpdateMapFlagFile(const std::string& map_dir);

struct MotionProfile;

using LanePosition = std::tuple<hdmap::LaneInfoConstPtr, double, double>;

// `distance` ahead of the `in_lane_pos` position of `in_lane`,
// we derive the `out_lane` and its position `out_lane_pos` .
LanePosition GetLanePositionAhead(const hdmap::LaneInfoConstPtr& in_lane, const double distance,
                                  const double in_lane_pos = 0);

LanePosition GetLanePositionFromXYPoint(const Obstacle::TrajectoryXYPoint& xypoint);

LanePosition GetLanePositionFromXY(const double x, const double y);

std::vector<double> GenerateVelocityProfile(double v0);
std::vector<double> GenerateVelocityProfile(const MotionProfile& profile, double v0, double dt);

std::tuple<double, double> GetXYFromLanePosition(const hdmap::LaneInfoConstPtr& lane_ptr,
                                                 const double s);

std::vector<Obstacle::TrajectoryXYPoint> GenerateObstacleLaneKeepTrajectory(
    const PlannerConfig& planner_config, Obstacle::TrajectoryXYPoint xypoint,
    const std::vector<double>& velocity_profile);
/*
  See ObstacleLaneChange for lane_change_type
*/
std::vector<Obstacle::TrajectoryXYPoint> GenerateObstacleLaneChangeTrajectory(
    const PlannerConfig& planner_config, Obstacle::TrajectoryXYPoint xypoint,
    const std::vector<double>& velocity_profile, const std::string& direction);

/* ===== Forward Declaration =====*/
struct LaneChangeObstacle;
struct LaneChangeDriverTriggerInfo;

std::optional<LaneChangeObstacle> GetLaneChangeObstacle(
    const PlannerConfig& planner_config,
    const std::map<double, LaneChangeObstacle>& lane_change_obstacle_map,
    const double simulation_time);

LaneChangeDriverTriggerInfo GetLaneChangeDriverTriggerInfo(
    const std::map<double, LaneChangeDriverTriggerInfo>& lane_change_obstacle_map,
    const double simulation_time);

PredictionObstacles ToProto(const std::map<int, Obstacle>& obstacle_map);

hdmap::LaneInfoConstPtr GetLeftLane(const hdmap::LaneInfoConstPtr& ego_lane);

hdmap::LaneInfoConstPtr GetRightLane(const hdmap::LaneInfoConstPtr& ego_lane);

hdmap::LaneInfoConstPtr GetPredecessorLane(const hdmap::LaneInfoConstPtr& ego_lane);

hdmap::LaneInfoConstPtr GetLaneById(std::string_view id);

double GetHeadingFromXY(const double x, const double y);

void AlignLocalizationWithHeading(LocalizationInfo& localization_info, const double heading);

void AlignLocalizationWithHeading(LocalizationInfo& localization_info);

}  // namespace t2::planning::test
