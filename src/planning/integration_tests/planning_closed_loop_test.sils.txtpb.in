scenario_name: "PlanningClosedLoopTestScenario"

spawn_configs_path: "/apollo/modules/planning/integration_tests/spawn/planning_closed_loop_test_components.spawn.txtpb"

validator_spawn_configs {
  components: {
    library_path: "/apollo/bazel-bin/modules/planning/integration_tests/validators/libplanning_closed_loop_validator.so"
    name: "planning_closed_loop_validator"
    class_name: "PlanningClosedLoopValidator"
  }
}

read_video: false
