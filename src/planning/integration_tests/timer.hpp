// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once
#include <chrono>

namespace t2::planning::test {

struct Timer {
  Timer() : begin_(std::chrono::system_clock::now()) {}
  double elapsed() const {
    auto now = std::chrono::system_clock::now();
    std::chrono::duration<double> time_diff = now - begin_;
    return time_diff.count();
  }

 private:
  std::chrono::time_point<std::chrono::system_clock> begin_;
};

inline void sleep(const double s) {
  test::Timer timer;
  while (timer.elapsed() < s) {
  }
}

}  // namespace t2::planning::test
