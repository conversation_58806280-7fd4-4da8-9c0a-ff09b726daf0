// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <canbus_msgs/msg/chassis.hpp>                      // Chassis
#include <localization_msgs/msg/localization_estimate.hpp>  // LocalizationEstimate
#include <planning_msgs/msg/adc_trajectory.hpp>             // ADCTrajectory
#include <planning_msgs/msg/planning_request.hpp>           // PlanningRequest
#include <planning_msgs/msg/planning_test_message.hpp>      // PlanningTestMessage
#include <planning_msgs/msg/truck_maker_message_out.hpp>    // TruckMakerMessageOut
#include <planning_msgs/msg/update_parameter.hpp>           // UpdateParameter
#include <prediction_msgs/msg/prediction_obstacles.hpp>     // PredictionObstacles
#include <rclcpp/rclcpp.hpp>                                // rclcpp::Node

#include "grace/execution/executor2/include/executor2/apex_node_base.hpp"
#include "src/planning/config/planner_config.hpp"                  // PlannerConfig
#include "src/planning/integration_tests/planning_test_input.hpp"  // PlanningTestInput
#include "src/planning/planning_dir_prefix.hpp"                    // FULL_PLANNING_DIR_PREFIX
#include "src/planning/planning_macros.hpp"                        // REGISTER_*_PLANNING_MSG
#include "src/planning/truckmaker/utility.hpp"                     // FromMessage, ToMessage
#include "tf2_ros/static_transform_broadcaster.h"  // tf2_ros::StaticTransformBroadcaster
#include "timer.hpp"                               // Timer

namespace t2::planning::test {

REGISTER_INTER_PREDICTION_MSG(PredictionObstacles);
REGISTER_INTER_LOCALIZATION_MSG(LocalizationEstimate);
REGISTER_INTER_CHASSIS_MSG(Chassis);
REGISTER_INTER_PLANNING_MSG(ADCTrajectory);
REGISTER_INTER_PLANNING_MSG(PlanningRequest);

REGISTER_INTER_PLANNING_MSG(UpdateParameter);
REGISTER_INTER_PLANNING_MSG(PlanningTestMessage);
REGISTER_INTER_PLANNING_MSG(TruckMakerMessageOut);

REGISTER_GEOMETRY_COMMON_MSG(TransformStamped);

class PlanningTestSimulationComponent final : public ::apex::executor::apex_node_base {
  using base = ::apex::executor::apex_node_base;
  rclcpp::Node& node_;

 public:
  PlanningTestSimulationComponent(const std::string& planning_test_input_filename,
                                  const bool use_sils = false);
  ~PlanningTestSimulationComponent() {}

  /* ===== Required Apex functions ===== */
  bool execute_impl() override;
  ::apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  ::apex::executor::subscription_list get_non_triggering_subscriptions_impl() const override;
  ::apex::executor::publisher_list get_publishers_impl() const override;

  /* ===== Writers =====*/
  ::rclcpp::Publisher<PlanningRequest>::SharedPtr planning_request_writer;
  ::rclcpp::Publisher<Chassis>::SharedPtr chassis_writer;
  ::rclcpp::Publisher<LocalizationEstimate>::SharedPtr localization_writer;
  ::rclcpp::Publisher<PredictionObstacles>::SharedPtr prediction_writer;

  ::rclcpp::Publisher<UpdateParameter>::SharedPtr
      update_parameter_writer;  ///< update planning, planner configs inside
                                ///< PlanningComponent
  ::rclcpp::Publisher<PlanningTestMessage>::SharedPtr
      planning_test_writer;  ///< send scenario to validators

  // std::unique_ptr<t2::adapter::TransformBroadcaster>
  //     tf2_broadcaster;  ///< publish transforms for FoxGlove
  tf2_ros::StaticTransformBroadcaster tf2_broadcaster;

  /* ===== Readers =====*/
  ::rclcpp::PollingSubscription<ADCTrajectory>::SharedPtr
      trajectory_reader;  ///< receive trajectories from PlanningComponent
  ::rclcpp::PollingSubscription<TruckMakerMessageOut>::SharedPtr
      truckmaker_data_out_reader;  ///< receive TruckMaker data output from
  //                                  ///< TruckMakerComponent
  // ::rclcpp::PollingSubscription<LocalizationEstimate>::SharedPtr<t2::common::PrettyHealth>
  //     pretty_health_reader;  ///< read HealthInfo in new API

  /* ===== END OF READERS, WRITERS =====*/

  /*===== Input Topics =====*/
  static constexpr char const* kChassisTopic = "/t2/canbus/chassis";
  static constexpr char const* kLocalizationPoseTopic = "/t2/localization/pose";
  static constexpr char const* kPredictionTopic = "/t2/prediction";

  static constexpr char const* kPlanningRequestTopic = "/t2/planning/planning_request";
  static constexpr char const* kUpdateParameterTopic = "/t2/planning/update_parameter";
  static constexpr char const* kPlanningTestTopic = "/t2/planning/test";

  static constexpr char const* kControlTopic = "/t2/control";
  static constexpr char const* kTruckMakerDataOutTopic =
      "/t2/planning/truckmaker_out";  ///< data received from TruckMaker

  static constexpr char const* kPlanningTopic = "/t2/planning";
  static constexpr char const* kPrettyHealthPlanningTopic =
      "/t2/pretty_health/planning_component";  ///< search
                                               ///< HealthReporter::kPrettyHealthChannelPrefix
                                               ///< + spawn_config.name()

  /*===== File Paths =====*/
  static constexpr char const* kPlannerConfigFile = PLANNING_DIR_PREFIX "conf/planner_config.json";

  static constexpr char const* kTruckMakerConfigFile =
      PLANNING_DIR_PREFIX "conf/truckmaker_config.json";

  PlanningTestInput planning_test_input;  ///< contains num_runs
  size_t i_run = 0;                       ///< current # of run

  void Proc();  ///< for closed loop without sils

  // Update inputs to Proc
  void UpdatePlanningInputFromTrajectory(const ADCTrajectory& adc_trajectory);

  std::tuple<PredictionObstacles, Chassis, LocalizationEstimate> GeneratePlanningComponentInput();
  bool IsCloseToStopPosition(const ADCTrajectory& adc_trajectory,
                             const StopPosition& stop_position) const;

  bool IsCloseToStopPosition(const TruckMakerDataOut& truckmaker_data_out,
                             const StopPosition& stop_position) const;

  void ProcPlanning();
  PlannerConfig planner_config;

  static constexpr char const* kPlanningTestSimulationComponentName =
      "planning_test_simulation_component";
  static constexpr char const* kPlanningTestSimulationNamespace =
      "planning_test_simulation_namespace";

 private:
  ADCTrajectory adc_trajectory_;           ///< read from kPlanningTopic
  TruckMakerDataOut truckmaker_data_out_;  ///< read from kTruckMakerDataOutTopic

  TransformStamped transform_stamped_;
  // t2::common::PrettyHealth pretty_health_;  ///< contains reported error and warn flags

  double init_timestamp = std::nan("");          ///< timestamp of the first cycle
  mutable double stop_timestamp = std::nan("");  ///< timestamp of stop_position

  std::optional<Timer> opt_timer_;  ///< time each cycle's run time
  std::map<int, std::vector<double>>
      obstacle_velocity_profile_map_;  ///< velocity profile for each obstacle

  void BroadcastTransform_(const LocalizationInfo& localization_info,
                           TransformStamped& transform_stamped);

  bool use_sils_ = false;  ///< if false, then no need of writers, readers
};

template <typename T>
inline bool FromFile(T& o, const std::string& filename) {
  try {
    std::ifstream json_stream(filename);
    cereal::JSONInputArchive archive(json_stream);
    archive(o);
  } catch (const std::exception& e) {
    T2_ERROR << "Error during deserialization: " << e.what();
    return false;
  }
  return true;
}

}  // namespace t2::planning::test
