// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "planning_closed_loop.hpp"  // PlanningClosedLoop

#include <iostream>
#include <sstream>

#include "planning_hdmap_utils.hpp"                    // GetLanePosition*
#include "planning_test_input.hpp"                     // PlanningTestInput
#include "src/common/config/map_config.hpp"            // common::config::MapConfig
#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException
#include "timer.hpp"                                   // Timer

namespace planning = ::t2::planning;
namespace common = ::t2::common;
namespace test = ::t2::planning::test;
namespace hdmap = ::t2::map::hdmap;

int main(int argc, char const* argv[]) {
  const std::string planning_test_input_filename =
      (argc <= 1) ? "/t2/modules/planning/.github/"
                    "planning_closed_loop_input_files/planning_proc_input.json"
                  : std::string(argv[1]);

  T2_INFO << "planning_test_input_filename=" << planning_test_input_filename;

  test::PlanningTestInput planning_test_input;
  if (!test::FromFile(planning_test_input, planning_test_input_filename)) {
    throw planning::PlanningModuleException("Invalid planning test input",
                                            planning::PlanningModuleErrorCode::INVALID_INPUT);
  }
  T2_INFO << "planning_test_input=\n" << planning::ToJsonString(PLANNING_VNP(planning_test_input));
  auto& localization_info = planning_test_input.localization_info;

  // Make map available to all components
  auto& map_config = ::t2::common::config::MapConfig::GetInstance();
  map_config.SetMapDir(planning_test_input.map_dir);

  std::string start_lane_id = "";
  hdmap::LaneInfoConstPtr lane_ptr;
  if (planning_test_input.start_lane_id.size()) {
    start_lane_id = planning_test_input.start_lane_id;
    lane_ptr = test::GetLaneById(start_lane_id);
    auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
    T2_INFO << std::setprecision(12) << "Start from start_lane_id=" << lane_ptr->id().id()
            << ", x=" << x << ", y=" << y;
    localization_info.x = x;
    localization_info.y = y;
  }
  test::AlignLocalizationWithHeading(localization_info);

  const auto hdmap = hdmap::HDMapUtil::BaseMapPtr();
  while (lane_ptr) {
    const size_t n_succ = lane_ptr->lane().successor_id_size();
    bool found = false;
    for (size_t i = 0; i < n_succ; ++i) {
      auto succ_lane = hdmap->GetLaneById(lane_ptr->lane().successor_id(i));
      if (!succ_lane) {
        continue;
      }
      auto branch_direction = succ_lane->lane().merge_info().branch_direction();
      if (branch_direction != hdmap::MergeInfo_BranchDirection_UNKNOWN) {
        auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
        T2_ERROR << std::setprecision(12) << "succ_lane=" << succ_lane->id().id() << ", x=" << x
                 << ", y=" << y << " has " << MergeInfo_BranchDirection_Name(branch_direction)
                 << ", so do not go (" << i << "/" << n_succ << ")";
        continue;
      }
      lane_ptr = succ_lane;
      found = true;
      break;
    }

    auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
    if (found) {
      T2_ERROR << std::setprecision(12) << "lane=" << lane_ptr->id().id() << ", x=" << x
               << ", y=" << y;
    } else {
      T2_ERROR << std::setprecision(12) << "lane=" << lane_ptr->id().id() << ", x=" << x
               << ", y=" << y << " does not have a successor";
      break;
    }
  }

  return 0;
}
