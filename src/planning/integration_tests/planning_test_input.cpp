// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "planning_test_input.hpp"

#include "planning_hdmap_utils.hpp"  // Get*

namespace t2::planning::test {

PlanningRequest LaneChangeDriverTriggerInfo::ToPlanningRequest(double x, double y) const {
  PlanningRequest planning_request;
  PlanningCommand command = PlanningCommand::UNSPECIFIED;
  if (direction == "right") {
    command = PlanningCommand::TURN_RIGHT_SIGNAL_ON;
    T2_INFO << "Driver trigger to right";
  } else if (direction == "left") {
    command = PlanningCommand::TURN_LEFT_SIGNAL_ON;
    T2_INFO << "Driver trigger to left";
  } else if (direction == "auto") {
    if (std::isfinite(x) && std::isfinite(y)) {
      auto [source_lane, _, __] = GetLanePositionFromXY(x, y);
      auto left_lane = GetLeftLane(source_lane);
      if (left_lane) {
        command = PlanningCommand::TURN_LEFT_SIGNAL_ON;
        T2_INFO << "Driver trigger to left";
      } else {
        auto right_lane = GetRightLane(source_lane);
        T2_PLAN_CHECK(right_lane) << "No available auto LC direction";
        command = PlanningCommand::TURN_RIGHT_SIGNAL_ON;
        T2_INFO << "Driver trigger to right";
      }
    }
  }

  // common::util::FillHeader("ADStateManager", &planning_request);
  planning_request.command = command;

  return planning_request;
}

int status_flag_to_int(std::string_view status_str) {
  int status_int = 0;
  if (status_str == "STATUS_FLAG_PLANNING_UNKNOWN") {
    status_int = 6000;
  } else if (status_str == "STATUS_FLAG_PLANNING_DISCONNECTED") {
    status_int = 6001;
  } else if (status_str ==
             "STATUS_FLAG_PLANNING_INPUT_PREDICTIONCOMMAND_ERROR_TOO_"
             "FREQUENT") {
    status_int = 6002;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_PREDICTIONCOMMAND_ERROR_TIMEOUT") {
    status_int = 6003;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_PREDICTIONCOMMAND_WARN_TIMEOUT") {
    status_int = 6004;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_LOCALIZATION_ERROR_TOO_FREQUENT") {
    status_int = 6005;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_LOCALIZATION_ERROR_TIMEOUT") {
    status_int = 6006;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_LOCALIZATION_WARN_TIMEOUT") {
    status_int = 6007;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_CHASSIS_ERROR_TOO_FREQUENT") {
    status_int = 6008;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_CHASSIS_ERROR_TIMEOUT") {
    status_int = 6009;
  } else if (status_str == "STATUS_FLAG_PLANNING_INPUT_CHASSIS_WARN_TIMEOUT") {
    status_int = 6010;
  } else if (status_str == "STATUS_FLAG_PLANNING_CURRENTLY_OUTSIDE_MAP") {
    status_int = 6100;
  } else if (status_str == "STATUS_FLAG_PLANNING_WRONG_DIRECTION") {
    status_int = 6101;
  } else if (status_str == "STATUS_FLAG_PLANNING_LOCALIZATION_FAR_AWAY_FROM_CURRENT_LANE") {
    status_int = 6102;
  } else if (status_str == "STATUS_FLAG_PLANNING_RECEIVED_OBSTACLE_WITHOUT_TRAJECTORY") {
    status_int = 6103;
  } else if (status_str ==
             "STATUS_FLAG_PLANNING_RECEIVED_OBSTACLE_WITH_TOO_SHORT_"
             "TRAJECTORY") {
    status_int = 6104;
  } else if (status_str == "STATUS_FLAG_PLANNING_RECEIVED_OBSTACLES_WITH_SAME_ID") {
    status_int = 6105;
  } else if (status_str == "STATUS_FLAG_PLANNING_RECEIVED_NEGATIVE_SPEED") {
    status_int = 6106;
  }
  return status_int;
}

}  // namespace t2::planning::test
