{"configById": {"Plot!1nhnnhq": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/canbus/chassis.steering_wheel_angle_rad", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!4df2c1n": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.only_trajectory[0].path_point.kappa", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!4dz90lt": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/control.debug.mpc_status.error[0].lateral_error", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Image!23xemns": {"cameraState": {"distance": 20, "perspective": true, "phi": 60, "target": [0, 0, 0], "targetOffset": [0, 0, 0], "targetOrientation": [0, 0, 0, 1], "thetaOffset": 45, "fovy": 45, "near": 0.5, "far": 5000}, "followMode": "follow-pose", "scene": {}, "transforms": {}, "topics": {}, "layers": {}, "publish": {"type": "point", "poseTopic": "/move_base_simple/goal", "pointTopic": "/clicked_point", "poseEstimateTopic": "/initialpose", "poseEstimateXDeviation": 0.5, "poseEstimateYDeviation": 0.5, "poseEstimateThetaDeviation": 0.26179939}, "imageMode": {"imageTopic": "/apollo/sensor/camera/preview/video-front-far"}}, "Plot!l2zuq6": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lat_data.s[0]", "enabled": true, "color": "#af4fe3", "label": "Lateral Posi"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.front_right_corner_l[0]", "enabled": true, "color": "#f5774d", "label": "Planned Front Right Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.front_left_corner_l[0]", "enabled": true, "color": "#f7df71", "label": "Planned Front Left Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.front_left_corner_l", "enabled": true, "label": "Measured Front Left Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.front_right_corner_l", "enabled": true, "label": "Measured Front Right Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_lateral_position", "enabled": true, "label": "Measured Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.rear_right_corner_l[0]", "enabled": true, "label": "Planned Rear Right Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.rear_left_corner_l[0]", "enabled": true, "label": "Planned Rear Left Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.rear_right_corner_l", "enabled": true, "label": "Measured Rear Right Corner Lateral Position"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.rear_left_corner_l", "enabled": true, "label": "Measured Rear Left Corner Lateral Position"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!1aoruv1": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lat_data.v[0]", "enabled": true, "color": "#a631ef", "label": "Lateral Velocity"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!328p34o": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lane_change_progress", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!4i1ofth": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/canbus/chassis.speed_mps", "enabled": true}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.front_vehicle_speed", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!1j9bt0e": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.front_vehicle_distance", "enabled": true}, {"value": "/apollo/planning.d_min", "enabled": true, "timestampMethod": "receiveTime"}, {"value": "/apollo/planning.d_target", "enabled": true, "timestampMethod": "receiveTime"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!39a1az7": {"paths": [{"value": "/apollo/planning.lane_change_data.neighbor_lane_front_obs_s_adjusted", "enabled": true, "timestampMethod": "receiveTime", "label": "Neighbor lane front s"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.neighbor_lane_rear_obs_s_adjusted", "enabled": true, "label": "Neighbor lane rear s"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.neighbor_lane_rear_obs_s", "enabled": true}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lane_change_ego_s", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "StateTransitions!35mzqus": {"paths": [{"value": "/apollo/planning.planning_module_status.state", "timestampMethod": "receiveTime"}, {"value": "/apollo/planning.lane_change_data.lane_changeable", "timestampMethod": "receiveTime"}, {"value": "/apollo/canbus/chassis.signal.turn_signal", "timestampMethod": "receiveTime"}, {"value": "/apollo/canbus/chassis.engage_advice.advice", "timestampMethod": "receiveTime"}], "isSynced": true}, "Plot!u1immv": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/control.debug.mpc_status.error[0].heading_error", "enabled": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}}, "globalVariables": {}, "userNodes": {}, "playbackConfig": {"speed": 1}, "layout": {"first": {"first": {"first": "Plot!1nhnnhq", "second": "Plot!4df2c1n", "direction": "column"}, "second": {"first": "Plot!4dz90lt", "second": "Plot!u1immv", "direction": "column"}, "direction": "column", "splitPercentage": 56.31300646091191}, "second": {"first": {"first": "Image!23xemns", "second": {"first": "Plot!l2zuq6", "second": {"first": "Plot!1aoruv1", "second": "Plot!328p34o", "direction": "column"}, "direction": "column", "splitPercentage": 38.68691479256362}, "direction": "column", "splitPercentage": 32.07159796535745}, "second": {"first": {"first": "Plot!4i1ofth", "second": "Plot!1j9bt0e", "direction": "column", "splitPercentage": 51.1705685618729}, "second": {"first": "Plot!39a1az7", "second": "StateTransitions!35mzqus", "direction": "column", "splitPercentage": 42.52029617671255}, "direction": "column", "splitPercentage": 46.01577099377373}, "direction": "row", "splitPercentage": 37.47763864042932}, "direction": "row", "splitPercentage": 31.027027027027028}}