{"configById": {"Indicator!11kq0el": {"path": "/apollo/planning.debug.planning_module_status.state", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "False", "rules": [{"operator": "=", "rawValue": "0", "color": "#0089ff", "label": "Lane Follow"}, {"operator": "=", "rawValue": "1", "color": "#ecae00", "label": "Lane Change"}, {"operator": "=", "rawValue": "2", "color": "#e22020", "label": "Lane Change Cancel"}, {"operator": "=", "rawValue": "3", "color": "#f5ec08", "label": "Lane Change Complete"}]}, "Indicator!1lc0sc4": {"path": "/apollo/planning.lane_change_data.lane_changeable_left", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Left Lane Change not Allowed", "rules": [{"operator": "=", "rawValue": "true", "color": "#68e24a", "label": "Left Lane Change Allowed"}]}, "Indicator!3kda9n6": {"path": "/apollo/planning.lane_change_data.lane_changeable_right", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Right Lane Change not Allowed", "rules": [{"operator": "=", "rawValue": "true", "color": "#68e24a", "label": "Right Lane Change Allowed"}]}, "RawMessages!hgl0ql": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.time_since_winker"}, "RawMessages!17tgd0p": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.lane_change_progress"}, "Indicator!1ggfqo2": {"path": "/apollo/planning.lane_change_data.winker_time_lane_changeability", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Turn Signal < 1.0s", "rules": [{"operator": "=", "rawValue": "1.0", "color": "#68e24a", "label": "Turn Signal > 1.0s"}]}, "Indicator!2oc8hdu": {"path": "/apollo/planning.decision.vehicle_signal.turn_signal", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "False", "rules": [{"operator": "=", "rawValue": "0", "color": "#a0a0a0", "label": "Turn signal OFF"}, {"operator": "=", "rawValue": "1", "color": "#2a97cd", "label": "Turn Signal Left ON"}, {"operator": "=", "rawValue": "2", "color": "#2a97cd", "label": "Turn Signal Right ON"}]}, "Indicator!3kb47xv": {"path": "/apollo/planning.lane_change_data.lane_changeable_result", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Lane Change Not Started", "rules": [{"operator": "=", "rawValue": "true", "color": "#68e24a", "label": "Lane Change Started"}]}, "Indicator!3n8z7en": {"path": "/apollo/planning.lane_change_data.d_front_left", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Front Left Obstacle", "rules": [{"operator": ">", "rawValue": "0.0", "color": "#68e24a", "label": "Front Left Obstacle"}]}, "RawMessages!21g5t03": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_safe_front_left"}, "RawMessages!2xj3wua": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_front_left"}, "Indicator!2oar4ue": {"path": "/apollo/planning.lane_change_data.d_front_ego", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Front Ego Obstacle", "rules": [{"operator": ">", "rawValue": "0.0", "color": "#68e24a", "label": "Front Ego Obstacle"}]}, "Indicator!1pdbeww": {"path": "/apollo/planning.lane_change_data.d_front_right", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Front Right Obstacle", "rules": [{"operator": ">", "rawValue": "0.0", "color": "#68e24a", "label": "Front Right Obstacle"}]}, "RawMessages!3dqt76w": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_safe_ego"}, "RawMessages!45ueuzy": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_front_ego"}, "RawMessages!3v1mck4": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_safe_front_right"}, "RawMessages!z6wwvw": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_front_right"}, "Indicator!i4nugm": {"path": "/apollo/planning.lane_change_data.d_rear_left", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Rear Left Obstacle", "rules": [{"operator": ">", "rawValue": "0.0", "color": "#68e24a", "label": "Rear Left Obstacle"}]}, "RawMessages!3odvgne": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_safe_rear_left"}, "RawMessages!3yseekp": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_rear_left"}, "Indicator!zy0uhj": {"path": "/apollo/planning.lane_change_data.d_rear_right", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Rear Right Obstacle", "rules": [{"operator": ">", "rawValue": "0.0", "color": "#68e24a", "label": "Rear Right Obstacle"}]}, "RawMessages!4bsc4pd": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_safe_rear_right"}, "RawMessages!4d6vq0a": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_rear_right"}, "Indicator!z2ttbs": {"path": "/apollo/planning.lane_change_data.trigger_information.left_trigger_geofenced", "style": "background", "fallbackColor": "#00ff80", "fallbackLabel": "Left LC Allowed", "rules": [{"operator": "=", "rawValue": "true", "color": "#a0a0a0", "label": "Left LC GeoFenced"}]}, "Indicator!2p3psxy": {"path": "/apollo/planning.lane_change_data.trigger_information.left_lc_triggerable", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Left LC Blocked By Segment", "rules": [{"operator": "=", "rawValue": "true", "color": "#00ff80", "label": "Left LC Allowed By Segment"}]}, "RawMessages!owqiy3": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.trigger_information.left_lane_change_upper_limit"}, "RawMessages!2hg7x7a": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.trigger_information.left_lane_change_lower_limit"}, "Indicator!fyv906": {"path": "/apollo/planning.lane_change_data.trigger_information.right_trigger_geofenced", "style": "background", "fallbackColor": "#00ff80", "fallbackLabel": "Right LC Allowed", "rules": [{"operator": "=", "rawValue": "true", "color": "#a0a0a0", "label": "Right LC GeoFenced"}]}, "Indicator!1a3c03o": {"path": "/apollo/planning.lane_change_data.trigger_information.right_lc_triggerable", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Right LC Blocked By Segment", "rules": [{"operator": "=", "rawValue": "true", "color": "#00ff80", "label": "Right LC Allowed By Segment"}]}, "RawMessages!4gsutxk": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.trigger_information.right_lane_change_upper_limit"}, "RawMessages!36iqavw": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.trigger_information.right_lane_change_lower_limit"}, "Indicator!h8gnoq": {"path": "/hmi/notifications.events[0].type", "style": "background", "fallbackColor": "#ffffff", "fallbackLabel": "", "rules": [{"operator": "=", "rawValue": "3", "color": "#564274", "label": "LANE CHANGE TRIGGERED"}, {"operator": "=", "rawValue": "6", "color": "#008000", "label": "LANE CHANGE COMPLETE"}, {"operator": "=", "rawValue": "13", "color": "#ff991c", "label": "APPROACHING LANE CHANGE"}, {"operator": "=", "rawValue": "14", "color": "#008000", "label": "LANE CHANGE ALLOWED AREA"}, {"operator": "=", "rawValue": "5", "color": "#ff0000", "label": "LANE CHANGE CANCELED"}, {"operator": "=", "rawValue": "true", "color": "#ecb8fc", "label": "Label"}]}, "StateTransitions!3jjalp7": {"paths": [{"value": "/hmi/notifications.events[:].type", "timestampMethod": "receiveTime"}], "isSynced": true, "xAxisRange": 60, "showXAxisLabels": true}, "Indicator!3k8fqzz": {"path": "/apollo/planning.lane_change_data.lane_changeable_result", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Lane Change Not Started", "rules": [{"operator": "=", "rawValue": "true", "color": "#68e24a", "label": "Lane Change Started"}]}, "Plot!1w9etzt": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_front_left", "enabled": true, "label": "Distance Left Front Vehicle"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_safe_front_left", "enabled": true, "label": "Minimum Distance"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!8xe4uv": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_front_right", "enabled": true, "label": "Distance Right Front Vehicle"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_safe_front_right", "enabled": true, "label": "Minimum Distance"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!40sk3wm": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_rear_left", "enabled": true, "label": "Distance Left Rear Vehicle"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_safe_rear_left", "enabled": true, "label": "Minimum Distance"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!3nbt51k": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_rear_right", "enabled": true, "label": "Distance Right Rear Vehicle"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.d_safe_rear_right", "enabled": true, "label": "Minimum Distance"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Indicator!3k8y67s": {"path": "/apollo/planning.speed_planner.speed_planner_nlp_state", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "Ipopt fails", "rules": [{"operator": "=", "rawValue": "true", "color": "#68e24a", "label": "<PERSON><PERSON><PERSON> succeed"}]}, "Plot!3j5vsp": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.only_trajectory[:].v", "enabled": true, "label": "Planned speed"}], "minYValue": 0, "maxYValue": 24, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!5uygsz": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.only_trajectory[:].a", "enabled": true, "label": "Planned acceleration"}], "minYValue": -2.5, "maxYValue": 1.5, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!1y3gcz4": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[:].ttc", "enabled": true, "label": "TTC with front vehicle"}], "minYValue": 0, "maxYValue": 23, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!3f3vts6": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[:].d_front", "enabled": true, "label": "Distance with front vehicle (bumper to bumper)"}], "minYValue": 0, "maxYValue": 200, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!ix44jm": {"paths": [{"value": "/apollo/planning.speed_planner.obstacle_states[:].v", "enabled": true, "timestampMethod": "receiveTime", "label": "Speed of the front vehicle"}], "minYValue": 0, "maxYValue": 28, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!9wvwk0": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[:].d_target", "enabled": true, "label": "Target distance for ACC"}], "minYValue": 0, "maxYValue": 130, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!4eoiyeq": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[:].d_min", "enabled": true, "label": "Minimum distance for ACC"}], "minYValue": 0, "maxYValue": 50, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!vh1x76": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.only_trajectory[0].v", "enabled": true, "label": "Speed at t0"}], "minYValue": 0, "maxYValue": 24, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "followingViewWidth": 180}, "Plot!3fks4i8": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.only_trajectory[0].a", "enabled": true, "label": "Acceleration at t0"}], "minYValue": -2.2, "maxYValue": 1.3, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "followingViewWidth": 180}, "Plot!wx9ml": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[0].ttc", "enabled": true, "label": "TTC at t0"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!37o8ua9": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[0].d_front", "enabled": true, "label": "Distance with front vehicle (bumper to bumper)"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[0].d_target", "enabled": true, "label": "Target distance for ACC"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[0].d_min", "enabled": true, "label": "Minimum distance for ACC"}], "minYValue": 0, "maxYValue": 200, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "followingViewWidth": 180}, "Plot!1mruodg": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.speed_planner.obstacle_states[0].v", "enabled": true, "label": "Speed of the front vehicle"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!3e60dd0": {"paths": [{"value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.front_right_corner_l[:]", "enabled": true, "timestampMethod": "receiveTime", "label": "Planned front right corner position"}, {"value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.front_left_corner_l[:]", "enabled": true, "timestampMethod": "receiveTime", "label": "Planned front left corner position"}, {"value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.rear_right_corner_l[:]", "enabled": true, "timestampMethod": "receiveTime", "label": "Planned rear right corner position"}, {"value": "/apollo/planning.lane_change_data.corners_lateral_position.planned_corners_position.rear_left_corner_l[:]", "enabled": true, "timestampMethod": "receiveTime", "label": "Planned rear left corner position"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!3nhl96i": {"paths": [{"value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.front_right_corner_l", "enabled": true, "timestampMethod": "receiveTime", "label": "Measured front right corner position"}, {"value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.front_left_corner_l", "enabled": true, "timestampMethod": "receiveTime", "label": "Measured front left corner position"}, {"value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.rear_right_corner_l", "enabled": true, "timestampMethod": "receiveTime", "label": "Measured rear right corner position"}, {"value": "/apollo/planning.lane_change_data.corners_lateral_position.measured_corners_position.rear_left_corner_l", "enabled": true, "timestampMethod": "receiveTime", "label": "Measured rear left corner position"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "StateTransitions!42oyuba": {"paths": [{"value": "/apollo/planning.debug.speed_target_debug.target_speed_anticipation_enable", "timestampMethod": "receiveTime"}], "isSynced": true, "showXAxisLabels": true}, "Plot!2nfwe2": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.debug.speed_target_debug.distance_to_target_speed", "enabled": true, "label": "Distance to target speed"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.debug.speed_target_debug.breaking_distance", "enabled": true, "label": "Braking distance"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!3fz3hpn": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.debug.speed_target_debug.look_forward_speed_target", "enabled": true, "label": "Ahead target speed"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.only_trajectory[0].v", "enabled": true, "label": "Ego speed"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.debug.speed_target_debug.upper_speed_limit", "enabled": true, "label": "Upper speed limit"}, {"timestampMethod": "receiveTime", "value": "/apollo/planning.debug.speed_target_debug.target_speed", "enabled": true, "label": "Target speed limit "}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "RawMessages!8b98i8": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning"}, "RawMessages!2oa9f6o": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {"header": true, "events": true, "0~events": true}}, "topicPath": "/hmi/notifications"}, "3D!29qs290": {"cameraState": {"perspective": false, "distance": 103.24633077843045, "phi": 100.210930009588, "thetaOffset": -0.21572387344207195, "targetOffset": [0.7925844396492842, 34.39964852696742, -3.712224747587633e-16], "target": [0, 0, 0], "targetOrientation": [0, 0, 0, 1], "fovy": 45, "near": 0.5, "far": 5000}, "followMode": "follow-pose", "followTf": "visualization_base", "scene": {"backgroundColor": "#000000"}, "transforms": {}, "topics": {"/apollo/perception/obstacles": {"visible": true}, "/apollo/planning": {"visible": true}, "/apollo/prediction": {"visible": true}, "/foxglove/hdmap": {"visible": true}, "/foxglove/vehicle": {"visible": true}}, "layers": {}, "publish": {"type": "point", "poseTopic": "/move_base_simple/goal", "pointTopic": "/clicked_point", "poseEstimateTopic": "/initialpose", "poseEstimateXDeviation": 0.5, "poseEstimateYDeviation": 0.5, "poseEstimateThetaDeviation": 0.26179939}, "imageMode": {}}, "Image!2hdlu13": {"cameraState": {"distance": 20, "perspective": true, "phi": 60, "target": [0, 0, 0], "targetOffset": [0, 0, 0], "targetOrientation": [0, 0, 0, 1], "thetaOffset": 45, "fovy": 45, "near": 0.5, "far": 5000}, "followMode": "follow-pose", "scene": {}, "transforms": {}, "topics": {}, "layers": {}, "publish": {"type": "point", "poseTopic": "/move_base_simple/goal", "pointTopic": "/clicked_point", "poseEstimateTopic": "/initialpose", "poseEstimateXDeviation": 0.5, "poseEstimateYDeviation": 0.5, "poseEstimateThetaDeviation": 0.26179939}, "imageMode": {"imageTopic": "/apollo/hdmaplocalization_img"}}, "Tab!4dspgy6": {"activeTabIdx": 0, "tabs": [{"title": "ALC (Changeability)", "layout": {"first": {"first": "Indicator!11kq0el", "second": {"first": "Indicator!1lc0sc4", "second": "Indicator!3kda9n6", "direction": "row"}, "direction": "column", "splitPercentage": 61.510067114093914}, "second": {"first": {"first": {"first": {"first": "RawMessages!hgl0ql", "second": "RawMessages!17tgd0p", "direction": "column"}, "second": {"first": "Indicator!1ggfqo2", "second": "Indicator!2oc8hdu", "direction": "row"}, "direction": "row"}, "second": {"first": "Indicator!3kb47xv", "second": {"first": {"first": "Indicator!3n8z7en", "second": {"first": "RawMessages!21g5t03", "second": "RawMessages!2xj3wua", "direction": "row", "splitPercentage": 52.054054054054056}, "direction": "column", "splitPercentage": 32.818290611943496}, "second": {"first": {"first": "Indicator!2oar4ue", "second": "Indicator!1pdbeww", "direction": "row"}, "second": {"first": {"first": "RawMessages!3dqt76w", "second": "RawMessages!45ueuzy", "direction": "row"}, "second": {"first": "RawMessages!3v1mck4", "second": "RawMessages!z6wwvw", "direction": "row"}, "direction": "row"}, "direction": "column", "splitPercentage": 32.48128286223567}, "direction": "row", "splitPercentage": 33.33333333333333}, "direction": "column", "splitPercentage": 25.83762149147392}, "direction": "column", "splitPercentage": 38.221869540611706}, "second": {"first": {"first": "Indicator!i4nugm", "second": {"first": "RawMessages!3odvgne", "second": "RawMessages!3yseekp", "direction": "row"}, "direction": "column", "splitPercentage": 38.289962825278835}, "second": {"first": "Indicator!zy0uhj", "second": {"first": "RawMessages!4bsc4pd", "second": "RawMessages!4d6vq0a", "direction": "row"}, "direction": "column", "splitPercentage": 37.93103448275865}, "direction": "row"}, "direction": "column", "splitPercentage": 70.13729977116705}, "direction": "column", "splitPercentage": 20.473157415832574}, "key": 1}, {"title": "ALC trigger info", "layout": {"first": {"first": {"first": {"first": "Indicator!z2ttbs", "second": "Indicator!2p3psxy", "direction": "column"}, "second": "RawMessages!owqiy3", "direction": "column", "splitPercentage": 83.72881355932205}, "second": "RawMessages!2hg7x7a", "direction": "column", "splitPercentage": 85.96406022340942}, "second": {"first": {"first": {"first": "Indicator!fyv906", "second": "Indicator!1a3c03o", "direction": "column"}, "second": "RawMessages!4gsutxk", "direction": "column", "splitPercentage": 83.72881355932205}, "second": "RawMessages!36iqavw", "direction": "column", "splitPercentage": 85.96406022340942}, "direction": "row", "splitPercentage": 48.992805755395686}, "key": 7}, {"title": "HMI", "layout": {"first": "Indicator!h8gnoq", "second": "StateTransitions!3jjalp7", "direction": "column"}, "key": 6}, {"title": "ALC (Distance Graphs)", "layout": {"first": {"first": "Indicator!3k8fqzz", "second": {"first": "Plot!1w9etzt", "second": "Plot!8xe4uv", "direction": "row", "splitPercentage": 49.966733200266134}, "direction": "column", "splitPercentage": 16.69980119284294}, "second": {"first": "Plot!40sk3wm", "second": "Plot!3nbt51k", "direction": "row"}, "direction": "column"}, "key": 4}, {"title": "ACC live", "layout": {"direction": "row", "second": {"first": {"first": "Plot!3f3vts6", "second": "Plot!ix44jm", "direction": "column"}, "second": {"first": "Plot!9wvwk0", "second": "Plot!4eoiyeq", "direction": "column"}, "direction": "column", "splitPercentage": 44.58293384467881}, "first": {"first": "Indicator!3k8y67s", "second": {"first": "Plot!3j5vsp", "second": {"first": "Plot!5uygsz", "second": "Plot!1y3gcz4", "direction": "column"}, "direction": "column", "splitPercentage": 34.957173447537464}, "direction": "column", "splitPercentage": 10.450623202301054}, "splitPercentage": 34.025022886786694}, "key": 2}, {"title": "ACC history", "layout": {"first": {"first": "Plot!vh1x76", "second": {"first": "Plot!3fks4i8", "second": "Plot!wx9ml", "direction": "column"}, "direction": "column"}, "second": {"first": "Plot!37o8ua9", "second": "Plot!1mruodg", "direction": "column"}, "direction": "row"}, "key": 5}, {"title": "Corners position", "layout": {"first": "Plot!3e60dd0", "second": "Plot!3nhl96i", "direction": "column", "splitPercentage": 44.58293384467881}, "key": 3}, {"title": "Speed limit trigger", "layout": {"first": {"first": "StateTransitions!42oyuba", "second": "Plot!2nfwe2", "direction": "column"}, "second": "Plot!3fz3hpn", "direction": "column"}, "key": 8}, {"title": "Raw messages", "key": 9, "layout": {"first": "RawMessages!8b98i8", "second": "RawMessages!2oa9f6o", "direction": "row"}}]}, "RawMessages!vpy76g": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.d_front_ego"}, "RawMessages!4a0ro7e": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.trigger_information.left_trigger_geofenced"}, "RawMessages!3ywv2vt": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "expansion": {"type": "collapsed", "inverses": {}}, "topicPath": "/apollo/planning.lane_change_data.trigger_information.right_trigger_geofenced"}, "Indicator!ortmj7": {"path": "/apollo/canbus/chassis.driving_mode", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "False", "rules": [{"operator": "=", "rawValue": "1", "color": "#68e24a", "label": "AUTO"}, {"operator": "=", "rawValue": "0", "color": "#9d9a9a", "label": "MANUAL"}]}, "Indicator!1dbov9c": {"path": "/hmi/notifications.events[:].type", "style": "background", "fallbackColor": "#ffffff", "fallbackLabel": "", "rules": [{"operator": "=", "rawValue": "3", "color": "#564274", "label": "LANE CHANGE TRIGGERED"}, {"operator": "=", "rawValue": "6", "color": "#008000", "label": "LANE CHANGE COMPLETE"}, {"operator": "=", "rawValue": "13", "color": "#ff991c", "label": "APPROACHING LANE CHANGE"}, {"operator": "=", "rawValue": "14", "color": "#008000", "label": "LANE CHANGE ALLOWED AREA"}, {"operator": "=", "rawValue": "5", "color": "#ff0000", "label": "LANE CHANGE CANCELED"}, {"operator": "=", "rawValue": "16", "color": "#f2e9e9", "label": "Takeover request"}]}, "StateTransitions!3v7eqwm": {"paths": [{"value": "/hmi/notifications.events[:].type", "timestampMethod": "receiveTime"}], "isSynced": true, "showXAxisLabels": true}, "Plot!4888bcb": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/canbus/chassis.driving_mode", "enabled": true}], "minYValue": 0, "maxYValue": 2, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}}, "globalVariables": {"globalVariable": 1}, "userNodes": {}, "playbackConfig": {"speed": 1, "highlightTopics": ["/hmi/notifications"]}, "layout": {"direction": "row", "first": {"first": "3D!29qs290", "second": "Image!2hdlu13", "direction": "column"}, "second": {"first": "Tab!4dspgy6", "second": {"first": {"first": "RawMessages!vpy76g", "second": {"first": "RawMessages!4a0ro7e", "second": {"first": "RawMessages!3ywv2vt", "second": {"first": "Indicator!ortmj7", "second": "Plot!4888bcb", "direction": "row", "splitPercentage": 33.2375478927203}, "direction": "row", "splitPercentage": 8.017621145374438}, "direction": "row", "splitPercentage": 6.967213114754103}, "direction": "row", "splitPercentage": 12.296277082324464}, "second": {"first": "Indicator!1dbov9c", "second": "StateTransitions!3v7eqwm", "direction": "row"}, "direction": "column"}, "direction": "column", "splitPercentage": 76.28939828080229}, "splitPercentage": 34.664351851851855}}