{"configById": {"Plot!1y14zkm": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.theta0_error", "enabled": true, "label": "Error: theta0"}], "minYValue": -0.0005, "maxYValue": 0.0005, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!4zlpm1": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.l0_error", "enabled": true, "label": "Error: l0", "color": "#cc4fe3"}], "minYValue": -0.01, "maxYValue": 0.01, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!3lvyp0f": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.lat.s[:]", "enabled": true, "label": "path.lat.s"}], "minYValue": -3.5, "maxYValue": 3.5, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!1x9tex4": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.lat.v[:]", "enabled": true, "label": "path.lat.v"}], "minYValue": -0.03, "maxYValue": 0.03, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240}, "Plot!20cb9tg": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.lat.a[:]", "enabled": true, "label": "path.lat.a"}], "minYValue": -0.0001, "maxYValue": 0.0001, "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "index", "sidebarDimension": 240, "minXValue": 0}, "Plot!l2zuq6": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lat_data.s[0]", "enabled": true, "color": "#af4fe3", "label": "Lateral Posi"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!1bp6qmy": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lat_data.v[0]", "enabled": true, "color": "#dd9034", "label": "Longitudinal Velocity"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!urp6r3": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.heading_data.s[0]", "enabled": true, "label": "Heading"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!ycuume": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.heading_data.v[0]", "enabled": true, "label": "Heading velocity"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!dywe8w": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.lat.a[0]", "enabled": true, "color": "#9e3ee2", "label": "Lateral Acceleration"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!3goanz1": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.lon_data.a[0]", "enabled": true, "color": "#e3b34f", "label": "Longitudinal Acceleration"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Plot!1k6dm0b": {"paths": [{"timestampMethod": "receiveTime", "value": "/apollo/planning.lane_change_data.path_planner_nlp_data.lat.a[0]", "enabled": true, "color": "#9e3ee2", "label": "Lateral Acceleration"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "Indicator!13si4ae": {"path": "/apollo/planning.decision.vehicle_signal.turn_signal", "style": "background", "fallbackColor": "#a0a0a0", "fallbackLabel": "NONE", "rules": [{"operator": "=", "rawValue": "1", "color": "#68e24a", "label": "LEFT"}, {"operator": "=", "rawValue": "2", "color": "#68e24a", "label": "RIGHT"}, {"operator": "=", "rawValue": "3", "color": "#f2bd45", "label": "HAZARD"}]}, "SourceInfo!6s1lin": {}}, "globalVariables": {}, "userNodes": {}, "playbackConfig": {"speed": 1}, "layout": {"direction": "row", "first": {"first": "3D!3p0d8cc", "second": "Image!1sbz9n1", "direction": "column"}, "second": {"direction": "row", "first": {"first": {"first": "Plot!l2zuq6", "second": "Plot!1aoruv1", "direction": "row"}, "second": {"first": {"first": "Plot!dywe8w", "second": "Plot!1bp6qmy", "direction": "row"}, "second": {"first": "Plot!dywe8w", "second": "Plot!1bp6qmy", "direction": "row"}, "direction": "column", "splitPercentage": 32.69623125010202}, "direction": "column", "splitPercentage": 24.580537945466855}, "second": {"first": "Plot!1k6dm0b", "second": {"first": "Plot!3goanz1", "second": {"first": "Plot!4c5gwsh", "second": "Plot!3goanz1", "direction": "column"}, "direction": "column", "splitPercentage": 32.031927247746935}, "direction": "column", "splitPercentage": 24.738105496399335}, "splitPercentage": 60.8677881864404}, "splitPercentage": 30.963802878325335}}