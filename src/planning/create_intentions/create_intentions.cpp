// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/create_intentions/create_intentions.hpp"

#include <planning_msgs/msg/planning_module_status.hpp>  // PlanningModuleState
#include <planning_trajectory_msgs/msg/sl_point.hpp>     // SLPoint

#include "src/common/config/vehicle_config_helper.hpp"  // VehicleConfigHelper
#include "src/common/math/box2d.hpp"                    // Box2d
#include "src/common/math/vec2d.hpp"                    // Vec2d
#include "src/common/proto/geometry.pb.h"               // common::Point2D
#include "src/planning/planning_module_exception.hpp"   // PlanningModuleException

namespace t2::planning {

using ::t2::common::config::VehicleConfigHelper;

using SLPoint = planning_trajectory_msgs::msg::SLPoint;
using PlanningModuleState = planning_msgs::msg::PlanningModuleState;

common::Point2D GetTargetPositionXYFromReferenceLineAndS(const ReferenceLine& reference_line,
                                                         const double target_position_s) {
  SLPoint target_position_sl;
  target_position_sl.s = target_position_s;
  target_position_sl.l = 0.0;
  std::optional<common::math::Vec2d> opt_target_position_vec =
      reference_line.SLToXY(target_position_sl);
  T2_PLAN_CHECK(opt_target_position_vec);
  const auto& target_position_vec = *opt_target_position_vec;
  common::Point2D target_position_xy;
  target_position_xy.set_x(target_position_vec.x());
  target_position_xy.set_y(target_position_vec.y());
  return target_position_xy;
}

IntentionTaskData CreateIntention(
    const double current_speed,
    const std::shared_ptr<const IntentionTaskData> last_trajectory_intention_ptr,
    const std::string& timestamp_suffix, bool is_lane_changeable, const bool& is_lane_change,
    const TrajectoryPoint& planning_start_point, const PlanningVehicleState& aligned_vehicle_state,
    const PlanningModuleStatus& planning_module_status,
    const std::map<int, ObstacleInformation>& obs_infos,
    const lane_change_trigger::LaneChangeDirection lane_change_direction) {
  IntentionTaskData intention_task_data;
  intention_task_data.planning_start_point = planning_start_point;
  intention_task_data.aligned_vehicle_state = aligned_vehicle_state;
  intention_task_data.planning_module_status = planning_module_status;
  intention_task_data.obs_infos = obs_infos;
  intention_task_data.lane_change_direction = lane_change_direction;

  if (!is_lane_change) {
    intention_task_data.name = "normal" + timestamp_suffix;
    intention_task_data.reference_speed = current_speed;
    intention_task_data.lane_change_state = LaneChangeState::INVALID;
    intention_task_data.lane_change_progress = std::nan("");
    return intention_task_data;
  } else {
    if (last_trajectory_intention_ptr) {
      if (last_trajectory_intention_ptr->is_lane_change) {
        auto& last_intention = *last_trajectory_intention_ptr;
        intention_task_data.name = last_intention.name;
        intention_task_data.is_lane_change = last_intention.is_lane_change;
        intention_task_data.lane_change_end_s = last_intention.lane_change_end_s;
        intention_task_data.lane_change_end_point =
            last_intention.lane_change_end_point;  // common::Point2D
        intention_task_data.reference_speed = last_intention.reference_speed;
        intention_task_data.lane_change_state = last_intention.lane_change_state;
        intention_task_data.is_lane_changeable = is_lane_changeable;
        auto previous_lane_change_state = last_intention.lane_change_state;
        auto& current_lane_change_state = intention_task_data.lane_change_state;
        if (previous_lane_change_state == LaneChangeState::TO_CHANGE_WITH_WINKER &&
            is_lane_changeable) {
          current_lane_change_state = LaneChangeState::LANE_CHANGING;
        }

        return intention_task_data;
      }
    }
    intention_task_data.is_lane_changeable = is_lane_changeable;
    intention_task_data.name = "lane_change_" + timestamp_suffix;
    intention_task_data.is_lane_change = true;
    intention_task_data.reference_speed = current_speed;
    intention_task_data.lane_change_state = LaneChangeState::TO_CHANGE_WITH_WINKER;
    return intention_task_data;
  }
}

std::list<IntentionTaskData> IntentionCreator::CreateListIntentionTaskData(
    const std::shared_ptr<const IntentionTaskData> in_last_trajectory_intention_ptr,
    const PlanningVehicleState& aligned_vehicle_state, const TrajectoryPoint& planning_start_point,
    const PlanningModuleStatus& planning_module_status,
    const std::optional<double> opt_lane_change_progress, ReferenceLineMap& reference_line_map,
    const bool& is_lane_changeable, const double& reference_line_lateral_threshold,
    const std::map<int, ObstacleInformation>& obs_infos,
    const lane_change_trigger::LaneChangeDirection lane_change_direction, rclcpp::Node& node,
    const double cancelation_threshold, const bool disable_lc_cancel) {
  std::list<IntentionTaskData> list_intention_task_data;  ///< output
  SLPoint sl_from_adc_lane;

  // xy_point represents the ego position in the global coordinates
  // It will be used to compute the lateral distance of the different lanes from
  // the current position of the ego.
  const common::math::Vec2d xy_point(aligned_vehicle_state.x, aligned_vehicle_state.y);
  const std::string timestamp_suffix =
      " (" + std::to_string(node.get_clock()->now().seconds() * 1e9) + ")";
  const double current_speed = aligned_vehicle_state.linear_velocity;

  // -----------------------------------------------------------------------------------
  // Lane Follow Intention
  // -----------------------------------------------------------------------------------
  // Create a normal intention to follow the `lc_source_lane`.
  const ReferenceLineAndRouteSegments& lc_source_lane =
      reference_line_map.at(ReferenceLineType::LANE_CHANGE_SOURCE);
  IntentionTaskData lane_follow_intention =
      CreateIntention(current_speed, in_last_trajectory_intention_ptr, timestamp_suffix,
                      is_lane_changeable, false, planning_start_point, aligned_vehicle_state,
                      planning_module_status, obs_infos, lane_change_direction);

  lane_follow_intention.lanes = lc_source_lane.route_segments;
  list_intention_task_data.emplace_back(lane_follow_intention);

  // -----------------------------------------------------------------------------------
  // Lane Change Intention
  // -----------------------------------------------------------------------------------
  // If `LANE_CHANGE_TARGET` exists, create an intention to change lanes to
  // that lane.
  // Note: If the last state of the planning_module_status is
  // LANE_CHANGE_COMPLETE, then we do not create a lane change intention to
  // correctly reset the lane_change_variables. Otherwise, we may misuse the
  // lane_change_variables from the last trajectory.
  if (reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET)) {
    const ReferenceLineAndRouteSegments& lc_destination_lane =
        reference_line_map.at(ReferenceLineType::LANE_CHANGE_TARGET);
    const auto& reference_line = lc_destination_lane.reference_line;
    const auto& route_segment = lc_destination_lane.route_segments;
    if (!opt_lane_change_progress) {
      T2_ERROR << "Failed to calculate lane change progress.";
    }

    const auto opt_sl_from_target_lane = reference_line.XYToSL(xy_point);
    if (!opt_sl_from_target_lane) {
      T2_ERROR << "Fail to compute SLPoint for a reference line; exit "
                  "CreateListIntentionTaskData";
      return list_intention_task_data;
    }

    auto lane_change_intention = [&]() -> IntentionTaskData {
      // Verifies if the lane has already started.
      // If it is the case the lane change, if disable_lc_cancel si true, the
      // lane change cannot be canceled.
      const bool lane_change_has_started =
          planning_module_status.state == PlanningModuleState::LANE_CHANGE;

      const bool lane_change_has_exceeded_half =
          opt_lane_change_progress && opt_lane_change_progress.value() >= cancelation_threshold;

      auto _is_lane_changeable = is_lane_changeable;
      if (lane_change_has_exceeded_half || (lane_change_has_started && disable_lc_cancel)) {
        T2_INFO << "CreateListIntentionTaskData: lane_change_progress="
                << *opt_lane_change_progress;
        T2_INFO << "Value of disable_lc_cancel is " << disable_lc_cancel;
        _is_lane_changeable = true;
      }
      return CreateIntention(current_speed, in_last_trajectory_intention_ptr, timestamp_suffix,
                             _is_lane_changeable, true, planning_start_point, aligned_vehicle_state,
                             planning_module_status, obs_infos, lane_change_direction);
    }();

    lane_change_intention.lanes = route_segment;

    const auto& opt_sl_from_source_lane = std::optional<SLPoint>();
    lc_source_lane.reference_line.XYToSL(xy_point);
    if (!opt_sl_from_source_lane) {
      T2_ERROR << "Fail to compute SLPoint for a reference line on segment; "
                  "exit CreateListIntentionTaskData";
      return list_intention_task_data;
    }
    lane_change_intention.offset_to_adc_lane =
        opt_sl_from_target_lane->l - opt_sl_from_source_lane->l;

    if (opt_lane_change_progress) {
      T2_PLAN_CHECK(lane_change_intention.lane_change_state != LaneChangeState::INVALID)
          << "Intention name: " << lane_change_intention.name;
      lane_change_intention.lane_change_progress = opt_lane_change_progress.value();
    }

    list_intention_task_data.emplace_back(lane_change_intention);
  }

  // -----------------------------------------------------------------------------------
  // Initialize IntentionTaskData
  // -----------------------------------------------------------------------------------
  for (IntentionTaskData& intention_task_data : list_intention_task_data) {
    ReferenceLine& reference_line =
        GetSuitableReferenceLine(intention_task_data.is_lane_change, reference_line_map);

    auto& adc_sl_boundary = intention_task_data.adc_sl_boundary;
    // Verify if the vehicle longitudinale position is contained in the
    // reference line.
    const double len = reference_line.Length();
    if (adc_sl_boundary.end_s < 0 || adc_sl_boundary.start_s > len) {
      T2_WARN << "Vehicle SL " << rosidl_generator_traits::to_yaml(adc_sl_boundary)
              << " is not on reference line:[0, " << len << "]";
    }

    // Verify if the vehicle is laterally not too far from the reference line.
    if (adc_sl_boundary.start_l > reference_line_lateral_threshold ||
        adc_sl_boundary.end_l < -reference_line_lateral_threshold) {
      T2_ERROR << "Ego vehicle is too far away from reference line.";
      continue;
    }

    if (!InitIntentionSLBoundary(planning_start_point.path_point, reference_line,
                                 intention_task_data.adc_sl_boundary)) {
      T2_ERROR << "Failed to initialize intention name=" << intention_task_data.name;
      continue;
    }
  }

  T2_PLAN_CHECK(!list_intention_task_data.empty()) << "Empty list of IntentionTaskData";

  return list_intention_task_data;
}

bool IntentionCreator::InitIntentionSLBoundary(const PathPoint& path_point,
                                               ReferenceLine& reference_line,
                                               SLBoundary& adc_sl_boundary) {
  const auto& vehicle_config = VehicleConfigHelper::GetInstance().GetConfig();

  const double front_edge_to_center = vehicle_config.vehicle_param().front_edge_to_center();  // [m]
  const double back_edge_to_center = vehicle_config.vehicle_param().back_edge_to_center();    // [m]
  const double left_edge_to_center = vehicle_config.vehicle_param().left_edge_to_center();    // [m]
  const double right_edge_to_center = vehicle_config.vehicle_param().right_edge_to_center();  // [m]
  const double length = vehicle_config.vehicle_param().length();                              // [m]
  const double width = vehicle_config.vehicle_param().width();                                // [m]

  common::math::Vec2d position(path_point.x, path_point.y);
  common::math::Vec2d vec_to_center((front_edge_to_center - back_edge_to_center) / 2.0,
                                    (left_edge_to_center - right_edge_to_center) / 2.0);
  // (param.front_edge_to_center() - param.back_edge_to_center()) / 2.0,
  // (param.left_edge_to_center() - param.right_edge_to_center()) / 2.0);
  common::math::Vec2d center(position + vec_to_center.rotate(path_point.theta));
  common::math::Box2d box(center, path_point.theta, length, width
                          // param.length(),
                          // param.width()
  );
  if (!reference_line.GetSLBoundary(box, &adc_sl_boundary)) {
    T2_ERROR << "Failed to get ADC boundary from box: " << box.DebugString();
    return false;
  }
  return true;
}

}  // namespace t2::planning
