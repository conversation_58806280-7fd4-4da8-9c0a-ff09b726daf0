// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <optional>

#include <rclcpp/rclcpp.hpp>  // rclcpp::Node

#include "src/planning/common/intention_task_data.hpp"  // IntentionTaskData
// lane_change_trigger::LaneChangeDirection
#include "src/planning/common/obstacle.hpp"                         // Obstacle
#include "src/planning/reference_line/reference_line_provider.hpp"  // ReferenceLineProvider

namespace t2::planning {

// Create the planning problem conditions. Example of conditions are "maximum
// allowed speed" or "area to not enter". Multiple problems could be created. If
// that is the case, they will be solved in parallel by the planner.
class IntentionCreator {
 public:
  std::list<IntentionTaskData> CreateListIntentionTaskData(
      const std::shared_ptr<const IntentionTaskData> last_trajectory_intention_ptr,
      const PlanningVehicleState& aligned_vehicle_state,
      const TrajectoryPoint& planning_start_point,
      const PlanningModuleStatus& planning_module_status,
      const std::optional<double> opt_lane_change_progress, ReferenceLineMap& reference_line_map,
      const bool& is_lane_changeable, const double& reference_line_lateral_threshold,
      const std::map<int, ObstacleInformation>& obs_infos,
      const lane_change_trigger::LaneChangeDirection lane_change_direction, rclcpp::Node& node,
      const double cancelation_threshold, const bool disable_lc_cancel);

  bool InitIntentionSLBoundary(const PathPoint& path_point, ReferenceLine& reference_line,
                               SLBoundary& adc_sl_boundary);
};

}  // namespace t2::planning
