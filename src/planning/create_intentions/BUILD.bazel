load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "create_intentions",
    srcs = ["create_intentions.cpp"],
    hdrs = ["create_intentions.hpp"],
    copts = ["-DMODULE_NAME=\\\"planning\\\""],
    deps = [
        "//src/common/config:vehicle_config_helper",  # VehicleConfigHelper
        "//src/common/core",  # T2_*
        "//src/common/proto:geometry_cc_proto",  # common::Point2D
        "//src/planning/common:intention_task_data",
        "//src/planning/common:obstacle",  # Obstacle
        "//src/planning/reference_line:reference_line_provider",
        "@apex//grace/ros/rclcpp/rclcpp",  # rclcpp::Node
    ],
)
