// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <optional>

#include <planning_msgs/msg/corners_lateral_position.hpp>     // CornersLateralPosition
#include <planning_msgs/msg/ipopt_result.hpp>                 // IpoptResult
#include <planning_msgs/msg/lane_change_data.hpp>             // LaneChangeData
#include <planning_msgs/msg/measured_corners_position.hpp>    // MeasuredCornersPosition
#include <planning_msgs/msg/planned_corners_position.hpp>     // PlannedCornersPosition
#include <planning_trajectory_msgs/msg/sl_point.hpp>          // SLPoint
#include <planning_trajectory_msgs/msg/trajectory_point.hpp>  // TrajectoryPoint
#include <rclcpp/rclcpp.hpp>                                  // rclcpp::Node

#include "src/common/core/logging.hpp"                  // T2_*
#include "src/planning/common/input_message_info.hpp"   // ChassisInfo, LocalizationInfo
#include "src/planning/common/intention_task_data.hpp"  // IntentionTaskData
#include "src/planning/common/planning_vehicle_state/planning_vehicle_state.hpp"  // PlanningVehicleState
#include "src/planning/common/trajectory/trajectory.hpp"            // PlanningTrajectory
#include "src/planning/config/miscellaneous_config.hpp"             // MiscellaneousConfig
#include "src/planning/config/planner_config.hpp"                   // PlannerConfig
#include "src/planning/planning_macros.hpp"                         // REGISTER_*_MSG
#include "src/planning/reference_line/reference_line_provider.hpp"  // ReferenceLineProvider

namespace t2::planning {

REGISTER_INTER_TRAJECTORY_MSG(TrajectoryPoint);
REGISTER_INTER_TRAJECTORY_MSG(SLPoint);

REGISTER_INTER_PLANNING_MSG(LaneChangeData);
REGISTER_INTER_PLANNING_MSG(IpoptResult);
REGISTER_INTER_PLANNING_MSG(CornersLateralPosition);
REGISTER_INTER_PLANNING_MSG(PlannedCornersPosition);
REGISTER_INTER_PLANNING_MSG(MeasuredCornersPosition);
/**
 * @brief Select the most appropriate trajectory
 *
 * After generating multiple trajectories (one per intention) this function
 * compares each trajectory and selects the most appropriate one.
 */
class SelectTrajectory {
 public:
  void Select(
      // output
      ADCTrajectory& output_trajectory,
      std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
      PlanningTrajectory& prev_trajectory,
      // input
      const ReferenceLineMap& reference_line_map,
      const std::list<IntentionTaskData>& list_intention_task_data, const double current_time_stamp,
      const std::vector<TrajectoryPoint>& stitching_trajectory, const std::string replan_reason,
      const double start_timestamp, const PlannerConfig& planner_config,
      const PlanningConfiguration& planning_config, const LocalizationInfo& localization_info,
      const std::list<ReferenceLine>& reference_lines, const rclcpp::Clock::SharedPtr& clock);

  std::optional<LocalizationInfo> opt_previous_localization_info;
};

void LogTrajectories(double start_timestamp, const IntentionTaskData& intention_task_data,
                     const std::vector<TrajectoryPoint>& prev_trajectory,
                     double front_vehicle_distance, double front_vehicle_speed,
                     const PlannerConfig& planner_config);

void LogLongitudinalAndLateralData(
    LaneChangeData& lane_change_data, const IntentionTaskData& intention_task_data,
    const std::vector<TrajectoryPoint>& discretized_trajectory,
    const std::list<ReferenceLine>& reference_lines, const LocalizationInfo& localization_info,
    const std::optional<LocalizationInfo>& opt_previous_localization_info,
    const PlannerConfig& planner_config);

}  // namespace t2::planning
