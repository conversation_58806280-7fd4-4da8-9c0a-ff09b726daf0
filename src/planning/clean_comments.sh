#!/bin/bash

# Find all .cpp and .hpp files recursively from the current directory
find . -type f \( -name "*.cpp" -o -name "*.hpp" \) | while read -r file; do
  echo "Processing $file"
  # Remove lines starting with "// #include"
  # Create a temp file and then overwrite the original
  sed '/^\/\/[[:space:]]*#include/d' "$file" > "$file.tmp" && mv "$file.tmp" "$file"
done

echo "Done removing lines starting with '// #include'"

# Find all .cpp and .hpp files recursively from the current directory
find . -type f \( -name "BUILD.bazel" \) | while read -r file; do
  echo "Processing $file"
  # Remove lines starting with "// #include"
  # Create a temp file and then overwrite the original
  sed '/^[[:space:]]*#/d' "$file" > "$file.tmp" && mv "$file.tmp" "$file"
done

echo "Done removing lines starting with '// #include'"
