load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(default_visibility = ["//visibility:public"])

PLANNING_DUMMY_COPTS = ["-DMODULE_NAME=\\\"planning_dummy\\\""]

cc_binary(
    name = "libplanning_dummy_component.so",
    linkshared = True,
    linkstatic = False,
    deps = [
        ":dummy_component",
    ],
)

cc_library(
    name = "dummy_component",
    srcs = [
        "dummy_component.cpp",
    ],
    hdrs = [
        "dummy_component.hpp",  # PlanningDummyComponent
    ],
    copts = PLANNING_DUMMY_COPTS,
    deps = [
        "//src/common/core",  # T2_*
        "//src/interfaces/perception_msgs",  # inter-module perception messages
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "@apex//grace/execution/executor2",  # ::apex::executor::executable_item
    ],
)
