// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "src/planning/dummy_component/dummy_component.hpp"  // PlanningDummyComponent

namespace t2::planning::dummy {

PlanningDummyComponent::PlanningDummyComponent()
    : base(kPlanningDummyComponentName, kPlanningDummyNamespace), node_(get_rclcpp_node()) {
  /* ===== Readers =====*/
  perception_reader_ = get_rclcpp_node().create_polling_subscription<PerceptionObstacles>(
      kPerceptionObstacleTopic, rclcpp::DefaultQoS());

  /* ===== Writers =====*/
  perception_writer_ = get_rclcpp_node().create_publisher<PerceptionObstacles>(
      kPerceptionObstacleTopic, rclcpp::DefaultQoS());
}

PlanningDummyComponent::~PlanningDummyComponent() {}

void PlanningDummyComponent::Proc(const PerceptionObstacles& perception_obstacles) {
  (void)perception_obstacles;
}

bool PlanningDummyComponent::execute_impl() {
  auto perception_msgs{perception_reader_->take()};

  for (const auto& msg : perception_msgs) {
    if (msg.info().valid()) {
      Proc(msg.data());
    }
  }
  return true;
}

::apex::executor::subscription_list PlanningDummyComponent::get_triggering_subscriptions_impl()
    const {
  return {perception_reader_};
}

::apex::executor::subscription_list PlanningDummyComponent::get_non_triggering_subscriptions_impl()
    const {
  return {perception_reader_};
}

::apex::executor::publisher_list PlanningDummyComponent::get_publishers_impl() const {
  return {perception_writer_};
}

}  // namespace t2::planning::dummy
