// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <perception_msgs/msg/perception_obstacles.hpp>  // PerceptionObstacles

#include "grace/execution/executor2/include/executor2/apex_node_base.hpp"  //
// ::apex::executor::apex_node_base
// ::apex::executor::executable_item

namespace t2::planning::dummy {

class PlanningDummyComponent final : public
                                     // ::apex::executor::executable_item
                                     ::apex::executor::apex_node_base {
  using base =
      //::apex::executor::executable_item;
      ::apex::executor::apex_node_base;
  using PerceptionObstacles = ::perception_msgs::msg::PerceptionObstacles;
  rclcpp::Node& node_;

 public:
  PlanningDummyComponent();
  ~PlanningDummyComponent();

  /* ===== Required Apex functions ===== */
  bool execute_impl() override;
  ::apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  ::apex::executor::subscription_list get_non_triggering_subscriptions_impl() const override;
  ::apex::executor::publisher_list get_publishers_impl() const override;

  void Proc(const PerceptionObstacles& msg);

 private:
  /* ===== Writers =====*/
  ::rclcpp::Publisher<PerceptionObstacles>::SharedPtr perception_writer_;

  /* ===== Readers =====*/
  ::rclcpp::PollingSubscription<PerceptionObstacles>::SharedPtr perception_reader_;

  /* ===== END OF READERS, WRITERS =====*/

  /*===== Topics =====*/
  static constexpr char const* kPerceptionObstacleTopic = "/t2/perception";

  static constexpr char const* kPlanningDummyComponentName = "planning_dummy_component";
  static constexpr char const* kPlanningDummyNamespace = "planning_dummy_namespace";
};

}  // namespace t2::planning::dummy
