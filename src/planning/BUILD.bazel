load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = [
    "-DMODULE_NAME=\\\"planning\\\"",
]

cc_library(
    name = "planning_macros",
    srcs = [],
    hdrs = [
        "planning_macros.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [],
)

cc_library(
    name = "planning_common",
    hdrs = [
        "planning_common.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning:planning_module_exception",  # PlanningModuleException
    ],
)

filegroup(
    name = "runtime_data",
    srcs = glob([
        "conf/*.json",
    ]),
)

cc_library(
    name = "planning_component",
    srcs = [
        "planning_communications.cpp",
        "planning_component.cpp",
    ],
    hdrs = [
        "planning_component.hpp",
    ],
    copts = PLANNING_COPTS,
    data = [
        "//src/planning:runtime_data",  # "conf/*.json"
    ],
    deps = [
        ":speed_limit_trigger",
        ":planning_module_state",  # UpdatePlanningModuleState
        ":planning_dir_prefix",  # FULL_PLANNING_DIR_PREFIX
        "//src/common/contract_assertions",
        "//src/common/core",  # T2_*
        "//src/common/transform",
        "//src/map/hdmap:hdmap_util",  # HDMapUtil
        "//src/interfaces/perception_msgs",  # PerceptionObstacles
        "//src/interfaces/prediction_msgs",  # PredictionObstacles
        "//src/interfaces/localization_msgs",  # LocalizationEstimate
        "//src/interfaces/canbus_msgs",  # Chassis
        "//src/common/proto:geometry_cc_proto",  # common::PointENU
        "//src/planning:planning_common",  # REGISTER_*_PLANNING_MSG
        "//src/planning/config:planning_config",  # PlanningConfiguration
        "//src/planning/reference_line",  # ReferenceLineAndRouteSegments, ReferenceLineMap
        "//src/planning/reference_line:reference_line_provider",  # ReferenceLineProvider
        "//src/planning/common/planning_vehicle_state",  # PlanningVehicleState, CreatePlanningVehicleState
        "//src/planning/planner",  # Planner
        "//src/planning/common:intention_task_data",  # IntentionTaskData
        "//src/planning/common/trajectory",  # PlanningTrajectory
        "//src/planning/common:input_message_info",  # PlanningTrajectory
        "//src/planning/create_intentions",  # CreateIntentions
        "//src/planning/select_trajectory",  # SelectTrajectory
        "//src/planning/lane_change_decider:lane_change",  # LaneChange
        "//src/planning/lane_change_decider",  # LaneChangeDecider
        "//src/planning/lane_change_decider:lane_change_decision",  # LaneChangeDecision
        "//src/planning/lane_change_decider/driver_trigger",  # DriverTrigger
        "//src/planning/common:trajectory_stitcher",  # ComputeReinitStitchingTrajectory
        "//src/common/math:euler_angles_zxy",  # EulerAnglesZXYd
        "//src/common/health:health_reporter",  # HealthReporter
        "//src/common/runfiles",  # RuntimeDependencyManager
        # ========================
        # Third-party dependencies
        # ========================
        "@apex//grace/interfaces/geometry_msgs",
        "@ros2.geometry2//tf2_ros:tf2_ros",  # geometry_msgs::msg::Vector3
        "@apex//common/cpputils",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",  # rclcpp::Node
    ],
)

cc_library(
    name = "planning_module_state",
    srcs = [
        "planning_module_state.cpp",
    ],
    hdrs = [
        "planning_module_state.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        ":planning_macros",
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning/common:intention_task_data",  # IntentionTaskData
        "//src/planning/config:planning_config",  # PlanningConfiguration
        "//src/planning/reference_line",  # ReferenceLineAndRouteSegments, ReferenceLineMap
    ],
)

cc_library(
    name = "speed_limit_trigger",
    srcs = [
        "speed_limit_trigger.cpp",
    ],
    hdrs = [
        "speed_limit_trigger.hpp",
    ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        "//src/interfaces/common_msgs",  # Advice
        "//src/interfaces/planning_msgs",  # ADCTrajectory
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/config:planner_config",
        "//src/planning/config:planning_config",
    ],
)

cc_library(
    name = "planning_module_exception",
    srcs = [
        "planning_module_exception.cpp",
    ],
    hdrs = [
        "planning_module_exception.hpp",
    ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        "//src/common/core",  # T2_*
        "//src/interfaces/common_msgs",  # StatusFlag
        "//src/planning/proto:planning_module_error_code_cc_proto",  # PlanningModuleErrorCode
    ],
)

# DUMMY_FILE_NAME = "dummy_file.tmp"

# genrule(
#     name = "generate_dummy_file",
#     srcs = glob([
#         "**/planning_git_hash.hpp",
#     ]),
#     outs = [DUMMY_FILE_NAME],
#     cmd = """# Remove planning_git_hash.hpp to enforce :planning_git_hash_source
#         WHOAMI=$$(whoami)
#         # echo "WHOAMI=$$WHOAMI"
#         PLANNING_GIT_HASH=$$(find /home/<USER>/.cache -type f -name planning_git_hash.hpp 2>/dev/null || true)
#         # echo "PLANNING_GIT_HASH=$$PLANNING_GIT_HASH"
#         rm -rf $$PLANNING_GIT_HASH 2>/dev/null || true
#         echo "This is {DUMMY_FILE_NAME}" > $@
#     """.format(DUMMY_FILE_NAME = DUMMY_FILE_NAME),
#     local = True,
# )

# genrule(
#     name = "planning_git_hash_source",
#     srcs = glob([
#         "**/planning_git_hash.hpp",
#     ]) + [
#         ":generate_dummy_file",
#     ],
#     outs = ["planning_git_hash.hpp"],
#     cmd = """
#         WHOAMI=$$(whoami)
#         # Generate planning_git_hash.hpp
#         (
#             echo \\#pragma once
#             echo \\#define PLANNING_COMPILE_TIME \\"$$(date +"%Y%m%d_%H%M%S")\\"
#             echo \\#define PLANNING_DIR_PREFIX \\"$$WHOAMI\\"

#             # Yatagarasu's branch and hash
#             echo \\#define YATAGARASU_GIT_HASH \\"$$(git -C /home/<USER>/Yatagarasu log -n 1 --pretty=format:"%H")\\"
#             YATAGARASU_GIT_BRANCH=$$(git -C /home/<USER>/Yatagarasu branch --show-current 2>/dev/null)
#             # echo "YATAGARASU_GIT_BRANCH=$$YATAGARASU_GIT_BRANCH"
#             if [ -z \\"$$YATAGARASU_GIT_BRANCH\\" ]; then
#               echo \\#define YATAGARASU_GIT_BRANCH \\"DETACHED_HEAD\\"
#             else
#               echo \\#define YATAGARASU_GIT_BRANCH \\"$$(echo $$YATAGARASU_GIT_BRANCH)\\"
#             fi
#         ) | tee $@

#         # Remove the dummy file
#         DUMMY_FILE=$$(find /home/<USER>/.cache -type f -name {DUMMY_FILE_NAME} 2>/dev/null || true)
#         # echo "DUMMY_FILE=$$DUMMY_FILE"
#         rm -rf $$DUMMY_FILE 2>/dev/null || true
#     """.format(DUMMY_FILE_NAME = DUMMY_FILE_NAME),
#     local = True,
# )

# cc_library(
#     name = "planning_git_hash_print",
#     srcs = [
#         "planning_git_hash_print.cpp",
#     ],
#     hdrs = [
#         "planning_git_hash_print.hpp",
#         ":planning_git_hash_source",
#     ],
#     copts = [
#         "-DMODULE_NAME=\\\"planning\\\"",
#     ],
#     deps = [
#     ],
# )

genrule(
    name = "planning_dir_prefix_gen",
    outs = ["planning_dir_prefix.hpp"],
    cmd = """
        WHOAMI=$$(whoami)
        PATH_TO_PLANNING_COMPONENT_HPP=$$(find ~ -type f -name "planning_component.hpp")
        DIR_OF_PLANNING_COMPONENT_HPP=$$(dirname "$$PATH_TO_PLANNING_COMPONENT_HPP")
        YATAGARASU_GIT_ROOT=$$(realpath "$$DIR_OF_PLANNING_COMPONENT_HPP/../..")
        echo "YATAGARASU_GIT_ROOT=$$YATAGARASU_GIT_ROOT"
        # Generate planning_dir_prefix.hpp
        (
            echo \\#pragma once
            echo \\#define PLANNING_DIR_PREFIX \\"src/planning/\\"
            echo \\#define FULL_YATAGARASU_DIR_PREFIX \\"$$YATAGARASU_GIT_ROOT/\\"
            echo \\#define FULL_PLANNING_DIR_PREFIX \\"$$YATAGARASU_GIT_ROOT/src/planning/\\"
        ) | tee $@
    """,
    local = True,
)

cc_library(
    name = "planning_dir_prefix",
    srcs = [
    ],
    hdrs = [
        "planning_dir_prefix.hpp",
    ],
    deps = [
        ":planning_dir_prefix_gen",
    ],
)
