// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/reference_line/reference_line_provider.hpp"

#include <algorithm>
#include <cmath>
#include <utility>

#include "src/common/config/vehicle_config_helper.hpp"         // VehicleConfigHelper
#include "src/common/core/logging.hpp"                         // T2_*
#include "src/common/math/vec2d.hpp"                           // Vec2d
#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager
#include "src/planning/config/miscellaneous_config.hpp"        // MiscellaneousConfig
#include "src/planning/planning_dir_prefix.hpp"                // FULL_PLANNING_DIR_PREFIX
#include "src/planning/planning_module_exception.hpp"          // PlanningModuleException

namespace t2::planning {

using ::t2::common::config::VehicleConfigHelper;
// using common::VehicleState;
using common::PointENU;
using common::math::Vec2d;
namespace hdmap = map::hdmap;
using hdmap::HDMapUtil;

template <typename T>
void uniform_slice(const T start, const T end, uint32_t num, std::vector<T>* sliced);

void ReferenceLineProvider::Init(const bool with_hdmap) {
  if (with_hdmap) {
    const hdmap::HDMap* base_map = HDMapUtil::BaseMapPtr();
    T2_PLAN_CHECK(base_map) << "Failed to load map";
    hdmap_ = base_map;
  }

  const std::string smoother_config_filename =
      common::runfiles::RuntimeDependencyManager::ResolvePath(PLANNING_DIR_PREFIX
                                                              "conf/smoother_config.json");
  std::ifstream is(smoother_config_filename);
  T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
      << "Cannot read " << smoother_config_filename;
  cereal::JSONInputArchive archive(is);  // serialize to JSON
  archive(cereal::make_nvp("smoother_config", smoother_config_));

  std::stringstream ss;
  cereal::JSONOutputArchive json_output(ss);
  json_output(cereal::make_nvp("smoother_config", smoother_config_));
  T2_INFO << "Reference line smoother config is:"
          << "\n"
          << ss.str();

  smoother_.config_ = smoother_config_;
}

bool ReferenceLineProvider::ValidateReferenceLineSmoothness(
    const ReferenceLine& raw_reference_line, const ReferenceLine& reference_line) const {
  for (double s = 0.0; s < reference_line.Length(); s += kReferenceLineDiffCheckStep) {
    auto xy_new = reference_line.GetMapPathPoint(s);

    SLPoint sl_new;
    if (!raw_reference_line.XYToSL(xy_new, &sl_new)) {
      T2_ERROR << "Fail to change xy point on smoothed reference line to sl "
                  "point respect to raw reference line.";
      return false;
    }

    const double diff = std::fabs(sl_new.l);
    if (diff > MiscellaneousConfig::getConfig().smoothed_reference_line_max_diff) {
      T2_ERROR << "Fail to provide reference line because too large diff "
                  "between smoothed and raw reference lines. diff: "
               << diff;
      return false;
    }
  }
  return true;
}

AnchorPoint ReferenceLineProvider::GetAnchorPoint(const ReferenceLine& reference_line,
                                                  double s) const {
  AnchorPoint anchor;
  anchor.longitudinal_bound = smoother_config_.longitudinal_boundary_bound;
  auto ref_point = reference_line.GetMapPathPoint(s);
  if (ref_point.lane_waypoints().empty()) {
    anchor.path_point = ref_point.ToPathPoint(s);
    anchor.lateral_bound = smoother_config_.max_lateral_boundary_bound;
    return anchor;
  }

  const auto& vehicle_config = VehicleConfigHelper::GetInstance().GetConfig();
  const double adc_width = vehicle_config.vehicle_param().width();

  const Vec2d left_vec = Vec2d::CreateUnitVec2d(ref_point.heading() + M_PI / 2.0);
  auto waypoint = ref_point.lane_waypoints().front();
  double left_width = 0.0;
  double right_width = 0.0;
  waypoint.lane->GetWidth(waypoint.s, &left_width, &right_width);
  const double kEpislon = 1e-8;
  double effective_width = 0.0;

  // shrink width by vehicle width, curb
  double safe_lane_width = left_width + right_width;
  safe_lane_width -= adc_width;
  bool is_lane_width_safe = true;

  if (safe_lane_width < kEpislon) {
    effective_width = kEpislon;
    is_lane_width_safe = false;
  }

  double center_shift = 0.0;
  if (route_lane_manager::RightBoundaryType(waypoint) == hdmap::LaneBoundaryType::CURB) {
    safe_lane_width -= smoother_config_.curb_shift;
    if (safe_lane_width < kEpislon) {
      effective_width = kEpislon;
      is_lane_width_safe = false;
    } else {
      center_shift += 0.5 * smoother_config_.curb_shift;
    }
  }
  if (route_lane_manager::LeftBoundaryType(waypoint) == hdmap::LaneBoundaryType::CURB) {
    safe_lane_width -= smoother_config_.curb_shift;
    if (safe_lane_width < kEpislon) {
      effective_width = kEpislon;
      is_lane_width_safe = false;
    } else {
      center_shift -= 0.5 * smoother_config_.curb_shift;
    }
  }

  //  apply buffer if possible
  const double buffered_width = safe_lane_width - 2.0 * smoother_config_.lateral_buffer;
  safe_lane_width = buffered_width < kEpislon ? safe_lane_width : buffered_width;

  // shift center depending on the road width
  if (is_lane_width_safe) {
    effective_width = 0.5 * safe_lane_width;
  }

  common::math::Vec2d shift = left_vec * center_shift;
  ref_point.set_x(ref_point.x() + shift.x());
  ref_point.set_y(ref_point.y() + shift.y());
  anchor.path_point = ref_point.ToPathPoint(s);
  anchor.lateral_bound =
      common::math::Clamp(effective_width, smoother_config_.min_lateral_boundary_bound,
                          smoother_config_.max_lateral_boundary_bound);
  return anchor;
}

std::vector<AnchorPoint> ReferenceLineProvider::GetAnchorPoints(
    const ReferenceLine& reference_line) const {
  std::vector<AnchorPoint> anchor_points;
  const double interval = smoother_config_.max_constraint_interval;
  int num_of_anchors = std::max(2, static_cast<int>(reference_line.Length() / interval + 0.5));
  std::vector<double> anchor_s;
  uniform_slice(0.0, reference_line.Length(), num_of_anchors - 1, &anchor_s);
  for (const double s : anchor_s) {
    AnchorPoint anchor = GetAnchorPoint(reference_line, s);
    anchor_points.push_back(anchor);
  }
  anchor_points[0].longitudinal_bound = anchor_points.back().longitudinal_bound = 1e-6;
  anchor_points[0].lateral_bound = anchor_points.back().lateral_bound = 1e-6;
  anchor_points[0].enforced = anchor_points.back().enforced = true;
  return anchor_points;
}

bool ReferenceLineProvider::CreateReferenceLineFromRouteSegmentSmoothing(
    const route_lane_manager::RouteSegments& segments, ReferenceLine& reference_line) {
  route_lane_manager::Path path(segments.lane_segments);
  const ReferenceLine raw_reference_line(path);
  bool result = SmoothReferenceLine(raw_reference_line, reference_line);
  reference_line.is_on_segment = segments.is_on_segment;
  return result;
}

bool ReferenceLineProvider::SmoothReferenceLine(const ReferenceLine& raw_reference_line,
                                                ReferenceLine& reference_line) {
  if (!MiscellaneousConfig::getConfig().enable_smooth_reference_line) {
    reference_line = raw_reference_line;
    return true;
  }
  // generate anchor points:
  const std::vector<AnchorPoint> anchor_points = GetAnchorPoints(raw_reference_line);
  smoother_.SetAnchorPoints(anchor_points);
  if (!smoother_.Smooth(raw_reference_line, reference_line)) {
    T2_ERROR << "Failed to smooth reference line with anchor points";
    return false;
  }
  if (!ValidateReferenceLineSmoothness(raw_reference_line, reference_line)) {
    T2_ERROR << "The smoothed reference line error is too large";
    return false;
  }
  return true;
}

PlannerEvaluationInfo ReferenceLineProvider::GetPlannerEvaluationInfo(
    const PlanningVehicleState& aligned_vehicle_state,
    const map::hdmap::LaneInfoConstPtr& ego_lane_info_ptr) {
  PlannerEvaluationInfo planner_evaluation_info;
  planner_evaluation_info.route_progress = 0.0;
  planner_evaluation_info.is_approaching_correct_request_waypoint = true;

  double s, l;
  common::math::Vec2d v{aligned_vehicle_state.x, aligned_vehicle_state.y};
  bool success = ego_lane_info_ptr->GetProjection(v, &s, &l);
  T2_PLAN_CHECK(success) << "Cannot get projection for x=" << v.x() << ", y=" << v.y();

  planner_evaluation_info.speed_limit = ego_lane_info_ptr->lane().speed_limit();
  planner_evaluation_info.lane_offset = l;
  planner_evaluation_info.distance_to_goal = 10.0;
  return planner_evaluation_info;
}

std::optional<route_lane_manager::RouteSegments>
ReferenceLineProvider::GetRouteSegmentsOfFollowingLane(const double backward_length,
                                                       const double forward_length,
                                                       const hdmap::LaneInfoConstPtr& lane_info_ptr,
                                                       const double lane_s,
                                                       const bool is_on_segment) const {
  T2_PLAN_CHECK(lane_info_ptr != nullptr);
  const route_lane_manager::LaneSegment current_segment(
      lane_info_ptr, std::max(0.0, lane_s - backward_length),
      std::min(lane_info_ptr->total_length(), lane_s + forward_length));

  auto current_lane_info_ptr = lane_info_ptr;

  // Trace forward until forward_length.
  double total_length = current_lane_info_ptr->total_length() - lane_s;
  std::list<route_lane_manager::LaneSegment> forward_lane_segments;
  while (total_length < forward_length) {
    const auto& successor_ids = current_lane_info_ptr->lane().successor_id();
    if (successor_ids.empty()) {
      T2_WARN << "No successor found for lane: " << current_lane_info_ptr->lane().id().id();
      break;
    }
    const auto next_lane_info_ptr = [&](const hdmap::HDMap* hdmap) -> hdmap::LaneInfoConstPtr {
      T2_PLAN_CHECK(hdmap != nullptr);
      for (const auto& successor_id : successor_ids) {
        const auto& lane = hdmap->GetLaneById(successor_id);
        if (lane == nullptr || lane->lane().merge_info().branch_direction() !=
                                   hdmap::MergeInfo_BranchDirection_UNKNOWN) {
          continue;
        }
        return lane;
      }
      return nullptr;
    }(hdmap_);
    if (next_lane_info_ptr == nullptr) {
      // In lane decrease scenarios, there may be no lane existing beyond the
      // end of the decreasing lane.
      T2_WARN << "No successor found for lane " << current_lane_info_ptr->id().id();
      break;
    }
    if (total_length + next_lane_info_ptr->total_length() >= forward_length) {
      const double length = forward_length - total_length;
      forward_lane_segments.emplace_back(next_lane_info_ptr, 0.0, length);
      break;
    } else {
      forward_lane_segments.emplace_back(next_lane_info_ptr, 0.0,
                                         next_lane_info_ptr->total_length());
    }
    total_length += next_lane_info_ptr->total_length();
    current_lane_info_ptr = next_lane_info_ptr;
  }

  // Trace backward until backward_length.
  total_length = lane_s;
  current_lane_info_ptr = lane_info_ptr;
  std::list<route_lane_manager::LaneSegment> backward_lane_segments;
  while (total_length < backward_length) {
    const auto& predecessor_ids = current_lane_info_ptr->lane().predecessor_id();
    if (predecessor_ids.empty()) {
      T2_WARN << "No predecessor found for lane: " << current_lane_info_ptr->lane().id().id();
      break;
    }
    const auto prev_lane_info_ptr = [&](const hdmap::HDMap* hdmap) -> hdmap::LaneInfoConstPtr {
      T2_PLAN_CHECK(hdmap != nullptr);
      for (const auto& predecessor_id : predecessor_ids) {
        const auto& lane = hdmap->GetLaneById(predecessor_id);
        // Only segment marked with UNKNOWN can be selected.
        // The RIGHT and LEFT branching and merging segment are filtered out.
        // This ensure that the backward reference line only goes "straight".
        if (lane == nullptr || lane->lane().merge_info().branch_direction() !=
                                   hdmap::MergeInfo_BranchDirection_UNKNOWN) {
          continue;
        }
        return lane;
      }
      return nullptr;
    }(hdmap_);
    if (!prev_lane_info_ptr) {
      // When the starting point of the route is near the starting point of the
      // lane, there may be cases where the predecessor lane is determined
      // not to be adjacent to the route. Hence, instead of throwing an error,
      // it just breaks
      break;
    }
    if (total_length + prev_lane_info_ptr->total_length() >= backward_length) {
      const double length = backward_length - total_length;
      backward_lane_segments.emplace_front(prev_lane_info_ptr,
                                           prev_lane_info_ptr->total_length() - length,
                                           prev_lane_info_ptr->total_length());
      break;
    } else {
      backward_lane_segments.emplace_front(prev_lane_info_ptr, 0.0,
                                           prev_lane_info_ptr->total_length());
    }
    total_length += prev_lane_info_ptr->total_length();
    current_lane_info_ptr = prev_lane_info_ptr;
  }

  // Concatenate backward_lane_segments, current_segment, and
  // forward_lane_segments.
  route_lane_manager::RouteSegments route_segments;
  route_segments.lane_segments.insert(route_segments.lane_segments.end(),
                                      backward_lane_segments.begin(), backward_lane_segments.end());
  route_segments.lane_segments.emplace_back(current_segment);
  route_segments.lane_segments.insert(route_segments.lane_segments.end(),
                                      forward_lane_segments.begin(), forward_lane_segments.end());
  route_segments.is_on_segment = is_on_segment;
  return route_segments;
}

std::optional<ReferenceLineAndRouteSegments>
ReferenceLineProvider::CreateFollowingReferenceLineAndRouteSegmentsFromLaneId(
    const map::hdmap::Id& lane_id, const double s, const bool is_on_segment,
    const double look_backward_distance, const double look_forward_distance) {
  const hdmap::LaneInfoConstPtr& lane_info_ptr = hdmap_->GetLaneById(lane_id);
  ReferenceLineAndRouteSegments lane;
  const auto get_route_segment_result = GetRouteSegmentsOfFollowingLane(
      look_backward_distance, look_forward_distance, lane_info_ptr, s, is_on_segment);
  if (!get_route_segment_result) {
    T2_ERROR << "Failed to get route segments of ego lane";
    return std::nullopt;
  }
  lane.route_segments = get_route_segment_result.value();
  CreateReferenceLineFromRouteSegmentSmoothing(lane.route_segments, lane.reference_line);
  return lane;
}

std::pair<std::vector<hdmap::LaneInfoConstPtr>, bool>
ReferenceLineProvider::GetSurroundingLaneInfoConstPtr(const PointENU& point,
                                                      const double heading) const {
  const double kMaxDistance = 10.0;  // meters.
  const double kHeadingBuffer = M_PI / 10.0;
  std::vector<hdmap::LaneInfoConstPtr> lanes;
  const int status = hdmap_->GetLanesWithHeading(point, kMaxDistance, heading,
                                                 M_PI / 2.0 + kHeadingBuffer, &lanes);
  if (status < 0) {
    T2_ERROR << "Failed to get lane from point: " << point.ShortDebugString();
    return std::make_pair(std::vector<hdmap::LaneInfoConstPtr>{}, false);
  }
  if (lanes.empty()) {
    T2_ERROR << "No valid lane found within " << kMaxDistance << " meters with heading " << heading;
    return std::make_pair(std::vector<hdmap::LaneInfoConstPtr>{}, false);
  }
  return std::make_pair(lanes, true);
}

// This function is created based on GetSurroundingLaneInfoConstPtr
// It needs to be refactored later for something clearer and more robust
bool ReferenceLineProvider::IsWithinMap(const common::PointENU& point, const double heading) const {
  const double max_distance = MiscellaneousConfig::getConfig().max_distance_within_map;  // meters.
  const double kHeadingBuffer = M_PI / 10.0;
  std::vector<hdmap::LaneInfoConstPtr> lanes;
  const int status = hdmap_->GetLanesWithHeading(point, max_distance, heading,
                                                 M_PI / 2.0 + kHeadingBuffer, &lanes);
  if (status < 0) {
    T2_ERROR << "Failed to get lane from point: " << point.ShortDebugString();
    return false;
  }
  if (lanes.empty()) {
    T2_ERROR << "No valid lane found within " << max_distance << " meters with heading " << heading;
    return false;
  }
  return true;
}

hdmap::LaneInfoConstPtr ReferenceLineProvider::FindEgoLane(
    const PlanningVehicleState& vehicle_state) {
  hdmap::LaneInfoConstPtr ego_lane_info_ptr;

  // Enumerate the nearest lanes
  common::PointENU point;
  point.set_x(vehicle_state.x);
  point.set_y(vehicle_state.y);
  point.set_z(vehicle_state.z);
  const auto& get_lane_info_const_ptr_result =
      GetSurroundingLaneInfoConstPtr(point, vehicle_state.heading);
  if (!get_lane_info_const_ptr_result.second) {
    T2_ERROR << "Failed to get nearest lane from point: " << point.ShortDebugString();
    return nullptr;
  }
  // The surrounding lanes are the lanes close to the ego vehicle.
  const auto& surrounding_lanes = get_lane_info_const_ptr_result.first;

  std::vector<hdmap::LaneInfoConstPtr> candidate_successors;
  std::vector<hdmap::LaneInfoConstPtr> candidate_lanes;
  // At initialization it is not possible to verify the successor
  // of the ego lane because it is necessary to determine which is the ego lane.
  if (ego_lane_) {
    // We remove all the lanes that are not the successor
    // of the current ego lane.
    // We remove the successor that is a branching.
    for (const auto& successor_lane_id : ego_lane_->lane().successor_id()) {
      for (const auto& surrounding_lane : surrounding_lanes) {
        if (surrounding_lane->id().id() == successor_lane_id.id() &&
            surrounding_lane->lane().merge_info().branch_direction() ==
                hdmap::MergeInfo_BranchDirection_UNKNOWN) {
          candidate_successors.push_back(surrounding_lane);
        }
      }
    }

    // We add all the candidate successors to the candidate lanes.
    candidate_lanes.insert(candidate_lanes.end(), candidate_successors.begin(),
                           candidate_successors.end());

    // We add the current ego_lane_ to the candidate lanes.
    // We add it because the ego vehicle still might be closer
    // to the ego lane determined in the previous cycle.
    candidate_lanes.push_back(ego_lane_);

    // We add the neighboring lanes of the ego lane and successors to the
    // candidate_lanes
    std::vector<hdmap::LaneInfoConstPtr> candidate_neighbors;
    for (const auto& candidate_lane : candidate_lanes) {
      // We add the left neighbor if it exist and it is not a branching.
      for (const auto& id : candidate_lane->lane().left_neighbor_forward_lane_id()) {
        for (const auto& surrounding_lane : surrounding_lanes) {
          if (surrounding_lane->id().id() == id.id() &&
              surrounding_lane->lane().merge_info().branch_direction() ==
                  hdmap::MergeInfo_BranchDirection_UNKNOWN) {
            candidate_neighbors.push_back(surrounding_lane);
          }
        }
      }

      // We add the right neighbor if it exist and it is not a branching.
      for (const auto& id : candidate_lane->lane().right_neighbor_forward_lane_id()) {
        for (const auto& surrounding_lane : surrounding_lanes) {
          if (surrounding_lane->id().id() == id.id() &&
              surrounding_lane->lane().merge_info().branch_direction() ==
                  hdmap::MergeInfo_BranchDirection_UNKNOWN) {
            candidate_neighbors.push_back(surrounding_lane);
          }
        }
      }
    }
    candidate_lanes.insert(candidate_lanes.end(), candidate_neighbors.begin(),
                           candidate_neighbors.end());
  } else {
    // We add the surrounding lanes to the candidate lanes if the surrounding
    // lane is a Branch Direction UNKNOWN. In fact, at that moment, we don't
    // have the ego lane and thus no successor.
    for (const auto& surrounding_lane : surrounding_lanes) {
      if (surrounding_lane->lane().merge_info().branch_direction() ==
          hdmap::MergeInfo_BranchDirection_UNKNOWN) {
        candidate_lanes.push_back(surrounding_lane);
      }
    }
  }

  // We select the lane that is the closest among the successor of the current
  // ego lane.
  double min_distance = std::numeric_limits<double>::infinity();
  for (const auto& lane_ptr : candidate_lanes) {
    const auto distance = lane_ptr->DistanceTo({vehicle_state.x, vehicle_state.y});
    if (distance < min_distance) {
      ego_lane_info_ptr = lane_ptr;
      min_distance = distance;
    }
  }

  ego_lane_ = ego_lane_info_ptr;
  return ego_lane_info_ptr;
}

CreateReferenceLineResult ReferenceLineProvider::CreateReferenceLine(
    const PlanningVehicleState& projected_vehicle_state, const PlanningConfiguration& config) {
  CreateReferenceLineResult creation_result;
  const auto ego_lane_info_ptr = FindEgoLane(projected_vehicle_state);
  if (ego_lane_info_ptr == nullptr) {
    T2_ERROR << "Failed to get nearest lane from point";
    return creation_result;
  }

  double point_s, point_l;
  if (!ego_lane_info_ptr->GetProjection({projected_vehicle_state.x, projected_vehicle_state.y},
                                        &point_s, &point_l)) {
    T2_ERROR << "Failed to get projection from point";
    return creation_result;
  }
  T2_INFO << "ego_lane: " << ego_lane_info_ptr->id().id();
  const auto& ego_lane = ego_lane_info_ptr->lane();
  const auto& opt_ego_lane = CreateFollowingReferenceLineAndRouteSegmentsFromLaneId(
      ego_lane.id(), point_s, true, config.route_lane_manager_config.look_backward_distance,
      config.route_lane_manager_config.look_forward_long_distance);
  if (opt_ego_lane) {
    creation_result.list_reference_lines.push_back(opt_ego_lane->reference_line);
    creation_result.list_route_segments.push_back(opt_ego_lane->route_segments);
  } else {
    T2_ERROR << "Failed to create following reference line of the ego lane.";
    return creation_result;
  }
  creation_result.opt_ego_lane = opt_ego_lane.value();

  // left neighbor lane
  if (ego_lane.left_neighbor_forward_lane_id_size() > 0) {
    const auto& left_lane_id = ego_lane.left_neighbor_forward_lane_id(0);
    const auto& opt_left_lane = CreateFollowingReferenceLineAndRouteSegmentsFromLaneId(
        left_lane_id, point_s, false, config.route_lane_manager_config.look_backward_distance,
        config.route_lane_manager_config.look_forward_long_distance);
    if (opt_left_lane) {
      creation_result.opt_left_lane = opt_left_lane.value();
      creation_result.list_reference_lines.push_back(opt_left_lane->reference_line);
      creation_result.list_route_segments.push_back(opt_left_lane->route_segments);
    } else {
      T2_ERROR << "Failed to create following reference line of the left lane.";
    }
  } else {
    T2_INFO << "ego lane does not have left neighbor lanes";
  }

  // right neighbor lane
  if (ego_lane.right_neighbor_forward_lane_id_size() > 0) {
    const auto& right_lane_id = ego_lane.right_neighbor_forward_lane_id(0);
    const auto& opt_right_lane = CreateFollowingReferenceLineAndRouteSegmentsFromLaneId(
        right_lane_id, point_s, false, config.route_lane_manager_config.look_backward_distance,
        config.route_lane_manager_config.look_forward_long_distance);
    if (opt_right_lane) {
      creation_result.opt_right_lane = opt_right_lane.value();
      creation_result.list_reference_lines.push_back(opt_right_lane->reference_line);
      creation_result.list_route_segments.push_back(opt_right_lane->route_segments);
    } else {
      T2_ERROR << "Failed to create following reference line of the right lane.";
    }
  } else {
    T2_INFO << "ego lane does not have right neighbor lanes";
  }
  return creation_result;
}

template <typename T>
void uniform_slice(const T start, const T end, uint32_t num, std::vector<T>* sliced) {
  if (!sliced || num == 0) {
    return;
  }
  const T delta = (end - start) / num;
  sliced->resize(num + 1);
  T s = start;
  for (uint32_t i = 0; i < num; ++i, s += delta) {
    sliced->at(i) = s;
  }
  sliced->at(num) = end;
}

}  // namespace t2::planning
