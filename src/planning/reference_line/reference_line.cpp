// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "reference_line.hpp"  // ReferenceLine

#include <boost/math/tools/minima.hpp>  // brent_find_minima

#include "absl/strings/str_cat.h"                           // StrCat
#include "absl/strings/str_join.h"                          // StrJoin
#include "src/common/math/cartesian_frenet_conversion.hpp"  // CartesianFrenetConverter
#include "src/common/math/linear_interpolation.hpp"         // slerp
#include "src/common/math/vec2d.hpp"                        // Vec2d
#include "src/common/util/string_util.hpp"                  // DebugStringFormatter
#include "src/planning/config/miscellaneous_config.hpp"     // MiscellaneousConfig
#include "src/planning/planning_module_exception.hpp"       // T2_PLAN_CHECK
#include "src/planning/route_lane_manager/path.hpp"         // InterpolatedIndex

namespace t2::planning {

using route_lane_manager::InterpolatedIndex;
using route_lane_manager::MapPathPoint;
namespace hdmap = map::hdmap;

ReferenceLine::ReferenceLine(const std::vector<MapPathPoint>& map_path_points)
    : map_path_points_(map_path_points),
      map_path_(
          std::move(std::vector<MapPathPoint>(map_path_points.begin(), map_path_points.end()))) {
  CHECK_EQ(static_cast<size_t>(map_path_.num_points()), map_path_points_.size());
}

ReferenceLine::ReferenceLine(const route_lane_manager::Path& map_path) : map_path_(map_path) {
  for (const auto& point : map_path.path_points()) {
    DCHECK(!point.lane_waypoints().empty());
    const auto& lane_waypoint = point.lane_waypoints()[0];
    PathPoint path_point;
    path_point.x = point.x();
    path_point.y = point.y();
    path_point.theta = point.heading();
    map_path_points_.emplace_back(path_point, lane_waypoint);
  }
  CHECK_EQ(static_cast<size_t>(map_path_.num_points()), map_path_points_.size());
}

bool ReferenceLine::Segment(const common::math::Vec2d& point, const double look_backward,
                            const double look_forward) {
  SLPoint sl;
  if (!XYToSL(point, &sl)) {
    T2_ERROR << "Failed to project point: " << point.DebugString();
    return false;
  }
  return Segment(sl.s, look_backward, look_forward);
}

bool ReferenceLine::Segment(const double s, const double look_backward, const double look_forward) {
  const auto& accumulated_s = map_path_.accumulated_s();

  // inclusive
  auto start_index = std::distance(
      accumulated_s.begin(),
      std::lower_bound(accumulated_s.begin(), accumulated_s.end(), s - look_backward));

  // exclusive
  auto end_index =
      std::distance(accumulated_s.begin(),
                    std::upper_bound(accumulated_s.begin(), accumulated_s.end(), s + look_forward));

  if (end_index - start_index < 2) {
    T2_ERROR << "Too few reference points after shrinking.";
    return false;
  }

  map_path_points_ = std::vector<MapPathPoint>(map_path_points_.begin() + start_index,
                                               map_path_points_.begin() + end_index);

  map_path_ = route_lane_manager::Path(
      std::vector<MapPathPoint>(map_path_points_.begin(), map_path_points_.end()));
  return true;
}

FrenetFramePoint ReferenceLine::GetFrenetPoint(const PathPoint& path_point) const {
  if (map_path_points_.empty()) {
    return FrenetFramePoint();
  }

  SLPoint sl_point;
  XYToSL(path_point, &sl_point);
  FrenetFramePoint frenet_frame_point;
  frenet_frame_point.s = sl_point.s;
  frenet_frame_point.l = sl_point.l;

  const double theta = path_point.theta;
  const double kappa = path_point.kappa;
  const double l = frenet_frame_point.l;

  MapPathPoint ref_point = GetMapPathPoint(frenet_frame_point.s);

  const double theta_ref = ref_point.heading();
  const double kappa_ref = ref_point.kappa();
  const double dkappa_ref = ref_point.dkappa();

  const double dl = common::math::CartesianFrenetConverter::CalculateLateralDerivative(
      theta_ref, theta, l, kappa_ref);
  const double ddl = common::math::CartesianFrenetConverter::CalculateSecondOrderLateralDerivative(
      theta_ref, theta, kappa_ref, kappa, dkappa_ref, l);
  frenet_frame_point.dl = dl;
  frenet_frame_point.ddl = ddl;
  return frenet_frame_point;
}

std::pair<std::array<double, 3>, std::array<double, 3>> ReferenceLine::ToFrenetFrame(
    const TrajectoryPoint& traj_point) const {
  T2_PLAN_CHECK(!map_path_points_.empty());

  SLPoint sl_point;
  XYToSL(traj_point.path_point, &sl_point);

  std::array<double, 3> s_condition;
  std::array<double, 3> l_condition;
  MapPathPoint ref_point = GetMapPathPoint(sl_point.s);
  const auto& path_point = traj_point.path_point;
  common::math::CartesianFrenetConverter::cartesian_to_frenet(
      sl_point.s, ref_point.x(), ref_point.y(), ref_point.heading(), ref_point.kappa(),
      ref_point.dkappa(), path_point.x, path_point.y, traj_point.v, traj_point.a, path_point.theta,
      path_point.kappa, &s_condition, &l_condition);

  return {s_condition, l_condition};
}

size_t ReferenceLine::GetNearestReferenceIndex(const double s) const {
  const auto& accumulated_s = map_path_.accumulated_s();
  if (s < accumulated_s.front() - 1e-2) {
    T2_WARN << "The requested s: " << s << " < 0.";
    return 0;
  }
  if (s > accumulated_s.back() + 1e-2) {
    T2_WARN << "The requested s: " << s << " > reference line length " << accumulated_s.back();
    return map_path_points_.size() - 1;
  }
  auto it_lower = std::lower_bound(accumulated_s.begin(), accumulated_s.end(), s);
  return std::distance(accumulated_s.begin(), it_lower);
}

std::vector<MapPathPoint> ReferenceLine::GetMapPathPoints(double start_s, double end_s) const {
  if (start_s < 0.0) {
    start_s = 0.0;
  }
  if (end_s > Length()) {
    end_s = Length();
  }
  std::vector<MapPathPoint> ref_points;
  auto start_index = GetNearestReferenceIndex(start_s);
  auto end_index = GetNearestReferenceIndex(end_s);
  if (start_index < end_index) {
    ref_points.assign(map_path_points_.begin() + start_index, map_path_points_.begin() + end_index);
  }
  return ref_points;
}

MapPathPoint ReferenceLine::GetMapPathPoint(const double s) const {
  const auto& accumulated_s = map_path_.accumulated_s();
  if (s < accumulated_s.front() - 1e-2) {
    T2_WARN << "The requested s: " << s << " < 0.";
    return map_path_points_.front();
  }
  if (s > accumulated_s.back() + 1e-2) {
    T2_WARN << "The requested s: " << s << " > reference line length: " << accumulated_s.back();
    return map_path_points_.back();
  }

  auto interpolate_index = map_path_.GetIndexFromS(s);

  size_t index = interpolate_index.id;
  size_t next_index = index + 1;
  if (next_index >= map_path_points_.size()) {
    next_index = map_path_points_.size() - 1;
  }

  const auto& p0 = map_path_points_[index];
  const auto& p1 = map_path_points_[next_index];

  const double s0 = accumulated_s[index];
  const double s1 = accumulated_s[next_index];
  return InterpolateWithMatchedIndex(p0, s0, p1, s1, interpolate_index);
}

double ReferenceLine::FindMinDistancePoint(const MapPathPoint& p0, const double s0,
                                           const MapPathPoint& p1, const double s1, const double x,
                                           const double y) {
  auto func_dist_square = [&p0, &p1, &s0, &s1, &x, &y](const double s) {
    auto p = Interpolate(p0, s0, p1, s1, s);
    double dx = p.x() - x;
    double dy = p.y() - y;
    return dx * dx + dy * dy;
  };

  return ::boost::math::tools::brent_find_minima(func_dist_square, s0, s1, 8).first;
}

MapPathPoint ReferenceLine::GetMapPathPoint(const double x, const double y) const {
  const auto func_distance_square = [](const MapPathPoint& point, const double target_x,
                                       const double target_y) {
    double dx = point.x() - target_x;
    double dy = point.y() - target_y;
    return dx * dx + dy * dy;
  };

  double d_min = func_distance_square(map_path_points_.front(), x, y);
  size_t index_min = 0;

  for (size_t i = 1; i < map_path_points_.size(); ++i) {
    double d_temp = func_distance_square(map_path_points_[i], x, y);
    if (d_temp < d_min) {
      d_min = d_temp;
      index_min = i;
    }
  }

  const size_t index_start = index_min == 0 ? index_min : index_min - 1;
  const size_t index_end = index_min + 1 == map_path_points_.size() ? index_min : index_min + 1;

  if (index_start == index_end) {
    return map_path_points_[index_start];
  }

  const double s0 = map_path_.accumulated_s()[index_start];
  const double s1 = map_path_.accumulated_s()[index_end];

  const double s = ReferenceLine::FindMinDistancePoint(map_path_points_[index_start], s0,
                                                       map_path_points_[index_end], s1, x, y);

  return Interpolate(map_path_points_[index_start], s0, map_path_points_[index_end], s1, s);
}

bool ReferenceLine::SLToXY(const SLPoint& sl_point, common::math::Vec2d* const xy_point) const {
  if (map_path_.num_points() < 2) {
    T2_ERROR << "The reference line has too few points.";
    return false;
  }

  const auto matched_point = GetMapPathPoint(sl_point.s);
  const auto angle = matched_point.heading();
  xy_point->set_x(matched_point.x() - sin(angle) * sl_point.l);
  xy_point->set_y(matched_point.y() + cos(angle) * sl_point.l);
  return true;
}

bool ReferenceLine::XYToSL(const common::math::Vec2d& xy_point, SLPoint* const sl_point) const {
  double s = 0.0;
  double l = 0.0;
  if (!map_path_.GetProjection(xy_point, &s, &l)) {
    T2_ERROR << "Cannot get nearest point from path.";
    return false;
  }
  sl_point->s = s;
  sl_point->l = l;
  return true;
}

std::optional<SLPoint> ReferenceLine::XYToSL(const common::math::Vec2d& xy_point) const {
  double s = 0.0, l = 0.0;
  if (!map_path_.GetProjection(xy_point, &s, &l)) {
    T2_ERROR << "Cannot get nearest point from path.";
    return std::nullopt;
  }
  SLPoint sl_point;
  sl_point.s = s;
  sl_point.l = l;
  return sl_point;
}

std::optional<common::math::Vec2d> ReferenceLine::SLToXY(const SLPoint& sl_point) const {
  if (map_path_.num_points() < 2) {
    T2_ERROR << "The reference line has too few points.";
    return std::nullopt;
  }

  const auto matched_point = GetMapPathPoint(sl_point.s);
  const auto angle = matched_point.heading();
  common::math::Vec2d xy_point;
  xy_point.set_x(matched_point.x() - sin(angle) * sl_point.l);
  xy_point.set_y(matched_point.y() + cos(angle) * sl_point.l);
  return xy_point;
}

MapPathPoint ReferenceLine::InterpolateWithMatchedIndex(const MapPathPoint& p0, const double s0,
                                                        const MapPathPoint& p1, const double s1,
                                                        const InterpolatedIndex& index) const {
  if (std::fabs(s0 - s1) < common::math::kMathEpsilon) {
    return p0;
  }
  double s = s0 + index.offset;
  DCHECK_LE(s0 - 1.0e-6, s) << "s: " << s << " is less than s0 : " << s0;
  DCHECK_LE(s, s1 + 1.0e-6) << "s: " << s << " is larger than s1: " << s1;

  auto map_path_point = map_path_.GetSmoothPoint(index);
  map_path_point.set_heading(common::math::slerp(p0.heading(), s0, p1.heading(), s1, s));
  map_path_point.set_kappa(common::math::lerp(p0.kappa(), s0, p1.kappa(), s1, s));
  map_path_point.set_dkappa(common::math::lerp(p0.dkappa(), s0, p1.dkappa(), s1, s));

  return map_path_point;
}

MapPathPoint ReferenceLine::Interpolate(const MapPathPoint& p0, const double s0,
                                        const MapPathPoint& p1, const double s1, const double s) {
  if (std::fabs(s0 - s1) < common::math::kMathEpsilon) {
    return p0;
  }
  DCHECK_LE(s0 - 1.0e-6, s) << " s: " << s << " is less than s0 :" << s0;
  DCHECK_LE(s, s1 + 1.0e-6) << "s: " << s << " is larger than s1: " << s1;

  const double x = common::math::lerp(p0.x(), s0, p1.x(), s1, s);
  const double y = common::math::lerp(p0.y(), s0, p1.y(), s1, s);
  const double heading = common::math::slerp(p0.heading(), s0, p1.heading(), s1, s);
  const double kappa = common::math::lerp(p0.kappa(), s0, p1.kappa(), s1, s);
  const double dkappa = common::math::lerp(p0.dkappa(), s0, p1.dkappa(), s1, s);
  std::vector<route_lane_manager::LaneWaypoint> waypoints;
  if (!p0.lane_waypoints().empty() && !p1.lane_waypoints().empty()) {
    const auto& p0_waypoint = p0.lane_waypoints()[0];
    if ((s - s0) + p0_waypoint.s <= p0_waypoint.lane->total_length()) {
      const double lane_s = p0_waypoint.s + s - s0;
      waypoints.emplace_back(p0_waypoint.lane, lane_s);
    }
    const auto& p1_waypoint = p1.lane_waypoints()[0];
    if (p1_waypoint.lane->id().id() != p0_waypoint.lane->id().id() &&
        p1_waypoint.s - (s1 - s) >= 0) {
      const double lane_s = p1_waypoint.s - (s1 - s);
      waypoints.emplace_back(p1_waypoint.lane, lane_s);
    }
    if (waypoints.empty()) {
      const double lane_s = p0_waypoint.s;
      waypoints.emplace_back(p0_waypoint.lane, lane_s);
    }
  }

  PathPoint path_point;
  path_point.x = x;
  path_point.y = y;
  path_point.theta = heading;
  path_point.kappa = kappa;
  path_point.dkappa = dkappa;
  return MapPathPoint(path_point);
}
bool ReferenceLine::GetLaneWidth(const double s, double& lane_left_width,
                                 double& lane_right_width) const {
  if (map_path_.path_points().empty()) {
    return false;
  }

  if (!map_path_.GetLaneWidth(s, lane_left_width, lane_right_width)) {
    return false;
  }
  return true;
}

std::pair<double, double> ReferenceLine::GetLaneWidth(const double s) const {
  if (map_path_.path_points().empty()) {
    return {std::nan(""), std::nan("")};
  }

  std::pair<double, double> p{std::nan(""), std::nan("")};
  if (!map_path_.GetLaneWidth(s, p.first, p.second)) {
    return {std::nan(""), std::nan("")};
  }
  return p;
}

bool ReferenceLine::GetRoadWidth(const double s, double* const road_left_width,
                                 double* const road_right_width) const {
  if (map_path_.path_points().empty()) {
    return false;
  }
  return map_path_.GetRoadWidth(s, road_left_width, road_right_width);
}

hdmap::LaneInfoConstPtr ReferenceLine::GetLaneFromS(const double s) const {
  auto ref_point = GetMapPathPoint(s);
  std::unordered_set<hdmap::LaneInfoConstPtr> lane_set;
  for (const auto& lane_waypoint : ref_point.lane_waypoints()) {
    auto result = lane_set.insert(lane_waypoint.lane);
    if (result.second) {
      // insertion succeeded => this lane was not present
      return lane_waypoint.lane;
    }
  }
  return nullptr;
}

bool ReferenceLine::IsOnLane(const common::math::Vec2d& vec2d_point) const {
  SLPoint sl_point;
  if (!XYToSL(vec2d_point, &sl_point)) {
    return false;
  }
  return IsOnLane(sl_point);
}

bool ReferenceLine::IsOnLaneWithNarrowingBuffer(const SLBoundary& sl_boundary,
                                                const double narrowing_buffer) const {
  if (sl_boundary.end_s < 0 || sl_boundary.start_s > Length()) {
    return false;
  }
  double middle_s = (sl_boundary.start_s + sl_boundary.end_s) / 2.0;
  double lane_left_width = 0.0;
  double lane_right_width = 0.0;
  map_path_.GetLaneWidth(middle_s, lane_left_width, lane_right_width);
  return sl_boundary.start_l <= lane_left_width - narrowing_buffer &&
         sl_boundary.end_l >= -lane_right_width + narrowing_buffer;
}

bool ReferenceLine::IsOnLane(const SLPoint& sl_point) const {
  if (sl_point.s <= 0 || sl_point.s > map_path_.length()) {
    return false;
  }
  double left_width = 0.0;
  double right_width = 0.0;

  if (!GetLaneWidth(sl_point.s, left_width, right_width)) {
    return false;
  }

  return sl_point.l >= -right_width && sl_point.l <= left_width;
}

bool ReferenceLine::GetSLBoundary(const common::math::Box2d& box,
                                  SLBoundary* const sl_boundary) const {
  std::vector<common::math::Vec2d> corners;
  box.GetAllCorners(&corners);

  // The order must be counter-clockwise
  std::vector<SLPoint> sl_corners;
  for (const auto& point : corners) {
    SLPoint sl_point;
    if (!XYToSL(point, &sl_point)) {
      T2_ERROR << "Failed to get projection for point: " << point.DebugString()
               << " on reference line.";
      return false;
    }
    sl_corners.push_back(std::move(sl_point));
  }

  for (size_t i = 0; i < corners.size(); ++i) {
    auto index0 = i;
    auto index1 = (i + 1) % corners.size();
    const auto& p0 = corners[index0];
    const auto& p1 = corners[index1];

    const auto p_mid = (p0 + p1) * 0.5;
    SLPoint sl_point_mid;
    if (!XYToSL(p_mid, &sl_point_mid)) {
      T2_ERROR << "Failed to get projection for point: " << p_mid.DebugString()
               << " on reference line.";
      return false;
    }

    common::math::Vec2d v0(sl_corners[index1].s - sl_corners[index0].s,
                           sl_corners[index1].l - sl_corners[index0].l);

    common::math::Vec2d v1(sl_point_mid.s - sl_corners[index0].s,
                           sl_point_mid.l - sl_corners[index0].l);

    sl_boundary->boundary_point.push_back(sl_corners[index0]);

    // sl_point is outside of polygon; add to the vertex list
    if (v0.CrossProd(v1) < 0.0) {
      sl_boundary->boundary_point.push_back(sl_point_mid);
    }
  }

  double start_s(std::numeric_limits<double>::max());
  double end_s(std::numeric_limits<double>::lowest());
  double start_l(std::numeric_limits<double>::max());
  double end_l(std::numeric_limits<double>::lowest());
  for (const auto& sl_point : sl_boundary->boundary_point) {
    start_s = std::fmin(start_s, sl_point.s);
    end_s = std::fmax(end_s, sl_point.s);
    start_l = std::fmin(start_l, sl_point.l);
    end_l = std::fmax(end_l, sl_point.l);
  }

  sl_boundary->start_s = start_s;
  sl_boundary->end_s = end_s;
  sl_boundary->start_l = start_l;
  sl_boundary->end_l = end_l;
  return true;
}

std::vector<route_lane_manager::LaneSegment> ReferenceLine::GetLaneSegments(
    const double start_s, const double end_s) const {
  return map_path_.GetLaneSegments(start_s, end_s);
}

bool ReferenceLine::GetSLBoundary(const hdmap::Polygon& polygon,
                                  SLBoundary* const sl_boundary) const {
  double start_s(std::numeric_limits<double>::max());
  double end_s(std::numeric_limits<double>::lowest());
  double start_l(std::numeric_limits<double>::max());
  double end_l(std::numeric_limits<double>::lowest());
  for (const auto& point : polygon.point()) {
    SLPoint sl_point;
    if (!XYToSL(point, &sl_point)) {
      T2_ERROR << "Failed to get projection for point: " << point.DebugString()
               << " on reference line.";
      return false;
    }
    start_s = std::fmin(start_s, sl_point.s);
    end_s = std::fmax(end_s, sl_point.s);
    start_l = std::fmin(start_l, sl_point.l);
    end_l = std::fmax(end_l, sl_point.l);
  }
  sl_boundary->start_s = start_s;
  sl_boundary->end_s = end_s;
  sl_boundary->start_l = start_l;
  sl_boundary->end_l = end_l;
  return true;
}

std::string ReferenceLine::DebugString() const {
  const auto limit = std::min(
      map_path_points_.size(),
      static_cast<size_t>(MiscellaneousConfig::getConfig().trajectory_point_num_for_debug));
  return absl::StrCat("point num:", map_path_points_.size(),
                      absl::StrJoin(map_path_points_.begin(), map_path_points_.begin() + limit, "",
                                    t2::common::util::DebugStringFormatter()));
}

std::vector<hdmap::LaneInfoConstPtr> ReferenceLine::GetLaneInfoPtrList() const {
  std::vector<hdmap::LaneInfoConstPtr> lane_ptr_list;
  for (const auto& map_path_point : map_path_points_) {
    const auto& lane_waypoints = map_path_point.lane_waypoints();
    for (const auto lane_waypoint : lane_waypoints) {
      const auto& lane_ptr = lane_waypoint.lane;
      if (lane_ptr) {
        if (lane_ptr_list.size() == 0) {
          lane_ptr_list.push_back(lane_ptr);
        } else if (lane_ptr_list.size() > 0 &&
                   lane_ptr_list.back()->id().id() != lane_ptr->id().id()) {
          lane_ptr_list.push_back(lane_ptr);
        }
      }
    }
  }
  return lane_ptr_list;
}

bool ReferenceLine::IsEqualTo(const ReferenceLine& other) const {
  const auto this_lane_ptr_list = GetLaneInfoPtrList();
  const auto other_lane_ptr_list = other.GetLaneInfoPtrList();

  const auto opt_match_result = [&]() -> std::optional<std::pair<size_t, size_t>> {
    for (size_t j = 0; j < this_lane_ptr_list.size(); j++) {
      const auto& this_lane_ptr = this_lane_ptr_list[j];
      for (size_t k = 0; k < other_lane_ptr_list.size(); k++) {
        const auto& other_lane_ptr = other_lane_ptr_list[k];
        if (this_lane_ptr->id().id() == other_lane_ptr->id().id()) {
          return std::make_pair(j, k);
        }
      }
    }
    return std::nullopt;
  }();
  if (!opt_match_result) {
    return false;
  }
  const auto this_start_index = opt_match_result->first;
  const auto other_start_index = opt_match_result->second;
  size_t this_index;
  size_t other_index;
  for (this_index = this_start_index, other_index = other_start_index;
       this_index < this_lane_ptr_list.size() && other_index < other_lane_ptr_list.size();
       this_index++, other_index++) {
    const auto& this_lane_ptr = this_lane_ptr_list[this_index];
    const auto& other_lane_ptr = other_lane_ptr_list[other_index];
    if (this_lane_ptr->id().id() != other_lane_ptr->id().id()) {
      return false;
    }
  }
  return true;
}

const ReferenceLine& GetSuitableReferenceLine(const bool is_lane_change,
                                              const ReferenceLineMap& reference_line_map) {
  if (is_lane_change) {
    T2_PLAN_CHECK(reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET))
        << "No LANE_CHANGE_TARGET reference line";
    return reference_line_map.at(ReferenceLineType::LANE_CHANGE_TARGET).reference_line;
  }
  T2_PLAN_CHECK(reference_line_map.count(ReferenceLineType::LANE_CHANGE_SOURCE))
      << "No LANE_CHANGE_SOURCE reference line";
  return reference_line_map.at(ReferenceLineType::LANE_CHANGE_SOURCE).reference_line;
}

ReferenceLine& GetSuitableReferenceLine(const bool is_lane_change,
                                        ReferenceLineMap& reference_line_map) {
  if (is_lane_change) {
    T2_PLAN_CHECK(reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET))
        << "No LANE_CHANGE_TARGET reference line";
    return reference_line_map.at(ReferenceLineType::LANE_CHANGE_TARGET).reference_line;
  }
  T2_PLAN_CHECK(reference_line_map.count(ReferenceLineType::LANE_CHANGE_SOURCE))
      << "No LANE_CHANGE_SOURCE reference line";
  return reference_line_map.at(ReferenceLineType::LANE_CHANGE_SOURCE).reference_line;
}

std::pair<SLPoint, double> GetSLAndHeadingToReferenceLine(
    const TrajectoryPoint& planning_start_point, const ReferenceLine& reference_line) {
  auto& path_point = planning_start_point.path_point;
  const common::math::Vec2d ego_position(path_point.x, path_point.y);
  const auto opt_ego_sl = reference_line.XYToSL(ego_position);
  T2_PLAN_CHECK(opt_ego_sl) << "Cannot get SLpoint for x=" << path_point.x
                            << ", y=" << path_point.y;
  const auto& ego_sl = *opt_ego_sl;
  const auto [s_condition, l_condition] = reference_line.ToFrenetFrame(planning_start_point);
  const double angle_diff = l_condition[1];
  return {ego_sl, angle_diff};
}

}  // namespace t2::planning
