load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = [
    "-DMODULE_NAME=\\\"planning\\\"",
]

cc_library(
    name = "reference_line",
    srcs = [
        "reference_line.cpp",
    ],
    hdrs = [
        "reference_line.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/common/config:vehicle_config_helper",  # VehicleConfigHelper
        "//src/common/math:cartesian_frenet_conversion",  # CartesianFrenetConverter
        "//src/common/math:linear_interpolation",  # slerp
        "//src/common/math:vec2d",  # Vec2d
        "//src/interfaces/planning_msgs",  # planning_msgs::msg::*
        "//src/interfaces/planning_trajectory_msgs",  # planning_trajectory_msgs::msg::*
        "//src/map/proto:map_cc_proto",  # hdmap::Polygon
        "//src/planning:planning_macros",  # REGISTER_*_MSG
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "//src/planning/config:miscellaneous_config",  # MiscellaneousConfig
        "//src/planning/route_lane_manager:path",  # MapPathPoint
        "//src/planning/route_lane_manager:route_segments",  # route_lane_manager::RouteSegments
        "@boost//:headers",  # brent_find_minima
        "@com_google_absl//absl/strings",  # StrCat, StrJoin  # StrCat, StrJoin
    ],
)

cc_library(
    name = "discrete_points_reference_line_smoother",
    srcs = ["discrete_points_reference_line_smoother.cpp"],
    hdrs = ["discrete_points_reference_line_smoother.hpp"],
    copts = PLANNING_COPTS,
    deps = [
        ":reference_line",
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/interfaces/planning_trajectory_msgs",  # PathPoint
        "//src/planning/config:smoother_config",  # ReferenceLineSmootherConfig
        "//src/planning/math:discrete_points_math",
        "//src/planning/math/discretized_points_smoothing:fem_pos_deviation_osqp_solver",
        "@cereal",
    ],
)

cc_library(
    name = "reference_line_provider",
    srcs = ["reference_line_provider.cpp"],
    hdrs = [
        "reference_line_provider.hpp",
    ],
    copts = PLANNING_COPTS,
    data = [
        "//src/planning:runtime_data",  # "conf/*.json"
    ],
    deps = [
        ":discrete_points_reference_line_smoother",
        ":reference_line",
        "//src/common/core",  # T2_*
        "//src/common/math:vec2d",  # Vec2d
        "//src/common/proto:geometry_cc_proto",  # PointENU
        "//src/common/runfiles",  # RuntimeDependencyManager
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_dir_prefix",  # FULL_PLANNING_DIR_PREFIX
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common/planning_vehicle_state",
        "//src/planning/config:miscellaneous_config",  # MiscellaneousConfig
        "//src/planning/config:planning_config",  # PlanningConfiguration
        "@eigen",
    ],
)
