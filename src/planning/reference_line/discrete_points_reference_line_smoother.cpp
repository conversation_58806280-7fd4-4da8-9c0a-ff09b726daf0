// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/reference_line/discrete_points_reference_line_smoother.hpp"

#include <algorithm>

#include <planning_trajectory_msgs/msg/sl_point.hpp>  // SLPoint

#include "reference_line.hpp"
#include "src/common/core/logging.hpp"                 // T2_*
#include "src/planning/math/discrete_points_math.hpp"  // DiscretePointsMath

namespace t2::planning {

using SLPoint = planning_trajectory_msgs::msg::SLPoint;

bool DiscretePointsReferenceLineSmoother::Smooth(const ReferenceLine& raw_reference_line,
                                                 ReferenceLine& smoothed_reference_line) {
  std::vector<std::pair<double, double>> raw_point2d;
  std::vector<double> anchorpoints_lateralbound;

  for (const auto& anchor_point : anchor_points_) {
    raw_point2d.emplace_back(anchor_point.path_point.x, anchor_point.path_point.y);
    anchorpoints_lateralbound.emplace_back(anchor_point.lateral_bound);
  }

  // fix front and back points to avoid end states deviate from the center of
  // road
  anchorpoints_lateralbound.front() = 0.0;
  anchorpoints_lateralbound.back() = 0.0;

  NormalizePoints(&raw_point2d);

  std::vector<std::pair<double, double>> smoothed_point2d;
  if (!FemPosSmooth(raw_point2d, anchorpoints_lateralbound, &smoothed_point2d)) {
    T2_ERROR << "discrete_points reference line smoother fails";
    return false;
  }

  DeNormalizePoints(&smoothed_point2d);

  std::vector<route_lane_manager::MapPathPoint> map_path_points;
  GenerateRefPointProfile(raw_reference_line, smoothed_point2d, &map_path_points);

  route_lane_manager::MapPathPoint::RemoveDuplicates(&map_path_points);
  if (map_path_points.size() < 2) {
    T2_ERROR << "Fail to generate smoothed reference line.";
    return false;
  }

  smoothed_reference_line = ReferenceLine(map_path_points);
  return true;
}

bool DiscretePointsReferenceLineSmoother::FemPosSmooth(
    const std::vector<std::pair<double, double>>& raw_point2d, const std::vector<double>& bounds,
    std::vector<std::pair<double, double>>* ptr_smoothed_point2d) {
  const auto& fem_pos_config = config_.fem_pos_deviation_smoothing;

  std::lock_guard<std::mutex> lock(mutex_);  ///< only one thread can use this at a time, to avoid
                                             ///< memory allocation and deallocation of osqp

  fem_pos_deviation_osqp_solver_.Init(fem_pos_config);

  // box contraints on pos are used in fem pos smoother, thus shrink the
  // bounds by 1.0 / sqrt(2.0)
  std::vector<double> box_bounds = bounds;
  const double box_ratio = 1.0 / std::sqrt(2.0);
  for (auto& bound : box_bounds) {
    bound *= box_ratio;
  }

  auto [opt_x, opt_y] = fem_pos_deviation_osqp_solver_.Solve(raw_point2d, box_bounds);
  if (opt_x.size() < 2 || opt_y.size() < 2) {
    T2_ERROR << "Return by fem pos smoother is wrong. Size smaller than 2 ";
    return false;
  }

  CHECK_EQ(opt_x.size(), opt_y.size()) << "x and y result size not equal";

  size_t point_size = opt_x.size();
  for (size_t i = 0; i < point_size; ++i) {
    ptr_smoothed_point2d->emplace_back(opt_x[i], opt_y[i]);
  }

  return true;
}

void DiscretePointsReferenceLineSmoother::SetAnchorPoints(
    const std::vector<AnchorPoint>& anchor_points) {
  CHECK_GT(anchor_points.size(), 1U);
  anchor_points_ = anchor_points;
}

void DiscretePointsReferenceLineSmoother::NormalizePoints(
    std::vector<std::pair<double, double>>* xy_points) {
  zero_x_ = xy_points->front().first;
  zero_y_ = xy_points->front().second;
  std::for_each(xy_points->begin(), xy_points->end(), [this](std::pair<double, double>& point) {
    auto curr_x = point.first;
    auto curr_y = point.second;
    std::pair<double, double> xy(curr_x - zero_x_, curr_y - zero_y_);
    point = std::move(xy);
  });
}

void DiscretePointsReferenceLineSmoother::DeNormalizePoints(
    std::vector<std::pair<double, double>>* xy_points) {
  std::for_each(xy_points->begin(), xy_points->end(), [this](std::pair<double, double>& point) {
    auto curr_x = point.first;
    auto curr_y = point.second;
    std::pair<double, double> xy(curr_x + zero_x_, curr_y + zero_y_);
    point = std::move(xy);
  });
}

bool DiscretePointsReferenceLineSmoother::GenerateRefPointProfile(
    const ReferenceLine& raw_reference_line,
    const std::vector<std::pair<double, double>>& xy_points,
    std::vector<route_lane_manager::MapPathPoint>* map_path_points) {
  // Compute path profile
  const auto [headings, accumulated_s, kappas, dkappas] =
      DiscretePointsMath::ComputePathProfile(xy_points);

  if (headings.empty()) {
    return false;
  }

  // Load into ReferencePoints
  size_t points_size = xy_points.size();
  for (size_t i = 0; i < points_size; ++i) {
    SLPoint ref_sl_point;
    if (!raw_reference_line.XYToSL({xy_points[i].first, xy_points[i].second}, &ref_sl_point)) {
      return false;
    }
    const double kEpsilon = 1e-6;
    if (ref_sl_point.s < -kEpsilon || ref_sl_point.s > raw_reference_line.Length()) {
      continue;
    }
    ref_sl_point.s = std::max(ref_sl_point.s, 0.0);
    // route_lane_manager::MapPathPoint
    auto rlp = raw_reference_line.GetMapPathPoint(ref_sl_point.s);
    auto new_lane_waypoints = rlp.lane_waypoints();
    for (auto& lane_waypoint : new_lane_waypoints) {
      lane_waypoint.l = ref_sl_point.l;
    }

    PathPoint path_point;
    path_point.x = xy_points[i].first;
    path_point.y = xy_points[i].second;
    path_point.theta = headings[i];
    path_point.kappa = kappas[i];
    path_point.dkappa = dkappas[i];

    map_path_points->emplace_back(route_lane_manager::MapPathPoint(path_point, new_lane_waypoints));
  }
  return true;
}

}  // namespace t2::planning
